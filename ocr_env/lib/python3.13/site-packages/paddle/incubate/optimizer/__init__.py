#   Copyright (c) 2020 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from . import functional  # noqa: F401
from .distributed_fused_lamb import DistributedFusedLamb  # noqa: F401
from .gradient_merge import GradientMergeOptimizer  # noqa: F401
from .lars_momentum import LarsMomentumOptimizer  # noqa: F401
from .lbfgs import LBFGS
from .lookahead import LookAhead  # noqa: F401
from .modelaverage import ModelAverage  # noqa: F401
from .pipeline import PipelineOptimizer  # noqa: F401
from .recompute import RecomputeOptimizer  # noqa: F401

__all__ = ['LBFGS']
