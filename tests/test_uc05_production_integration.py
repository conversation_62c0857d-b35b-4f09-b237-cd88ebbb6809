"""
🎯 UC05: Production Integration Tests
Comprehensive end-to-end testing of the production-quality liability analysis system
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
from datetime import datetime
from decimal import Decimal

# Import all production components
from backend.app.use_cases.uc05_liability.liability_analyzer import LiabilityAnalyzer
from backend.app.use_cases.uc05_liability.insufficient_data_handler import (
    InsufficientDataHandler, DataSufficiencyLevel
)
from backend.app.use_cases.uc05_liability.legal_precedent_engine import (
    LegalPrecedentEngine, LiabilityType, Province
)
from backend.app.use_cases.uc05_liability.workflow_decision_engine import (
    WorkflowDecisionEngine, WorkflowStage
)
from backend.app.use_cases.uc05_liability.enhanced_document_processor import (
    EnhancedDocumentProcessor, DocumentCategory
)
from backend.app.use_cases.uc05_liability.loss_quantum_calculator import (
    LossQuantumCalculator, DamageCategory
)
from backend.app.use_cases.uc05_liability.communication_generator import (
    ProfessionalCommunicationGenerator, CommunicationType, RecipientType
)
from backend.app.use_cases.uc05_liability.audit_compliance_system import (
    AuditComplianceSystem, AuditEventType
)

class TestUC05ProductionIntegration:
    """Comprehensive integration tests for UC05 production system"""
    
    @pytest.fixture
    def mock_ai_service(self):
        """Mock AI service for testing"""
        mock = AsyncMock()
        mock.generate_response.return_value = "Mock AI response"
        return mock
    
    @pytest.fixture
    def sample_documents(self):
        """Sample documents for testing"""
        return [
            {
                "filename": "fnol_report.pdf",
                "text": "First Notice of Loss - Claim Number: UC05-2024-001\nClaimant: Anna Thompson\nIncident Date: June 2, 2025\nLocation: Metro Grocery Store, Toronto, ON\nDescription: Customer slipped on wet floor near produce section",
                "confidence": 0.95
            },
            {
                "filename": "medical_report.pdf", 
                "text": "Medical Report - Patient: Anna Thompson\nDiagnosis: Mild wrist sprain\nTreatment: Rest, ice, compression\nEstimated recovery: 2-3 weeks\nTotal medical expenses: $1,500",
                "confidence": 0.92
            },
            {
                "filename": "incident_report.pdf",
                "text": "Store Incident Report\nDate: June 2, 2025\nTime: 2:30 PM\nLocation: Produce section\nDescription: Spill from leaking produce mister not immediately cleaned\nNo warning signs posted\nEmployee notified 10 minutes after incident",
                "confidence": 0.88
            },
            {
                "filename": "invoice.pdf",
                "text": "Emergency Room Invoice\nPatient: Anna Thompson\nDate of Service: June 2, 2025\nServices: X-ray, examination, splint\nTotal Amount: $1,500.00",
                "confidence": 0.94
            }
        ]
    
    @pytest.fixture
    def claim_context(self):
        """Sample claim context"""
        return {
            "claim_number": "UC05-2024-001",
            "claimant_name": "Anna Thompson",
            "incident_date": "June 2, 2025",
            "claim_type": "premises liability",
            "location": "Toronto, Ontario"
        }
    
    @pytest.mark.asyncio
    async def test_complete_liability_analysis_workflow(self, mock_ai_service, sample_documents, claim_context):
        """Test complete end-to-end liability analysis workflow"""
        
        # Initialize the production liability analyzer
        analyzer = LiabilityAnalyzer()
        analyzer.ai_service = mock_ai_service
        
        # Mock AI responses for different components
        mock_ai_service.generate_response.side_effect = [
            # Document classification responses
            "first_notice_of_loss",
            "medical_report", 
            "incident_report",
            "invoices_receipts",
            # Evidence extraction responses
            '{"injury_type": {"content": "mild wrist sprain", "confidence": 0.9}}',
            # Legal analysis responses
            "0.85",  # Fact similarity
            # Incident analysis
            '{"sequence_of_events": "Customer slipped on wet floor", "contributing_factors": ["No warning signs", "Delayed cleanup"]}',
            # Fault assessment
            '{"insured_fault": 80, "third_party_fault": 20, "reasoning": "Store failed to maintain safe premises", "risk_factors": {"coverage_adequacy": "sufficient"}}',
            # Financial document parsing
            '[{"description": "Emergency room treatment", "amount": "1500", "category": "medical"}]',
            # Pain and suffering calculation
            "moderate",
            # Settlement recommendation
            "moderate"
        ]
        
        # Run the complete analysis
        result = await analyzer.analyze_liability(sample_documents, claim_context)
        
        # Verify comprehensive results
        assert result is not None
        assert result.insured_fault_percentage > 0
        assert result.third_party_fault_percentage >= 0
        assert result.confidence_score > 0
        assert len(result.supporting_evidence) > 0
        
        # Verify production features are included
        assert result.data_sufficiency is not None
        assert result.legal_analysis is not None
        assert result.document_analyses is not None
        assert result.workflow_state is not None
        
        # Verify email drafts are generated for insufficient data
        if result.data_sufficiency.level in [DataSufficiencyLevel.INSUFFICIENT, DataSufficiencyLevel.CRITICAL_MISSING]:
            assert len(result.email_drafts) > 0
        
        # Verify next steps are provided
        assert len(result.next_steps) > 0
    
    @pytest.mark.asyncio
    async def test_insufficient_data_handling(self, mock_ai_service):
        """Test insufficient data detection and handling"""
        
        # Incomplete document set (missing medical reports)
        incomplete_documents = [
            {
                "filename": "fnol_only.pdf",
                "text": "Basic incident report with minimal details",
                "confidence": 0.7
            }
        ]
        
        handler = InsufficientDataHandler()
        handler.ai_service = mock_ai_service
        
        mock_ai_service.generate_response.return_value = "first_notice_of_loss"
        
        # Test data sufficiency assessment
        assessment = await handler.assess_data_sufficiency(incomplete_documents)
        
        # Verify insufficient data is detected
        assert assessment.level in [DataSufficiencyLevel.INSUFFICIENT, DataSufficiencyLevel.CRITICAL_MISSING]
        assert len(assessment.missing_documents) > 0
        assert not assessment.can_proceed
        assert len(assessment.email_drafts) > 0
        assert len(assessment.next_steps) > 0
    
    @pytest.mark.asyncio
    async def test_legal_precedent_analysis(self, mock_ai_service):
        """Test legal precedent matching and analysis"""
        
        engine = LegalPrecedentEngine()
        engine.ai_service = mock_ai_service
        
        # Mock similarity calculation
        mock_ai_service.generate_response.return_value = "0.9"
        
        case_facts = [
            "Slip and fall on wet floor",
            "No warning signs posted", 
            "Store employee aware of hazard"
        ]
        
        # Test legal analysis
        analysis = await engine.analyze_legal_precedents(
            case_facts, 
            LiabilityType.PREMISES_LIABILITY,
            Province.ON
        )
        
        # Verify legal analysis results
        assert analysis is not None
        assert len(analysis.applicable_precedents) > 0
        assert analysis.confidence_score > 0
        assert len(analysis.recommendations) > 0
        assert analysis.legal_reasoning is not None
    
    @pytest.mark.asyncio
    async def test_loss_quantum_calculation(self, mock_ai_service):
        """Test financial loss calculation"""
        
        calculator = LossQuantumCalculator()
        calculator.ai_service = mock_ai_service
        
        # Mock financial document parsing
        mock_ai_service.generate_response.return_value = '''[
            {"description": "Emergency room visit", "amount": "1500", "category": "medical"},
            {"description": "Lost wages", "amount": "800", "category": "wages"}
        ]'''
        
        financial_docs = [
            {
                "filename": "medical_bill.pdf",
                "text": "Medical expenses: $1,500 for emergency treatment"
            },
            {
                "filename": "wage_statement.pdf", 
                "text": "Lost wages: $800 for time off work"
            }
        ]
        
        fault_allocation = {"insured": 75, "third_party": 25}
        
        # Test loss quantum calculation
        result = await calculator.calculate_loss_quantum(financial_docs, fault_allocation)
        
        # Verify calculation results
        assert result is not None
        assert result.total_damages > 0
        assert result.insured_liability > 0
        assert result.settlement_recommendation > 0
        assert len(result.damage_breakdown) > 0
        assert result.calculation_confidence > 0
    
    @pytest.mark.asyncio
    async def test_communication_generation(self, mock_ai_service):
        """Test professional communication generation"""
        
        generator = ProfessionalCommunicationGenerator()
        generator.ai_service = mock_ai_service
        
        # Mock AI enhancement
        mock_ai_service.generate_response.return_value = "Enhanced professional communication"
        
        context_data = {
            "claim_number": "UC05-2024-001",
            "claimant_name": "Anna Thompson",
            "missing_documents": ["Medical report", "Witness statement"]
        }
        
        # Test communication generation
        communication = await generator.generate_communication(
            CommunicationType.DOCUMENT_REQUEST,
            RecipientType.CLAIMANT,
            context_data
        )
        
        # Verify communication results
        assert communication is not None
        assert communication.subject is not None
        assert communication.body is not None
        assert communication.urgency in ["low", "medium", "high"]
        assert communication.follow_up_date is not None
    
    @pytest.mark.asyncio
    async def test_workflow_decision_engine(self, mock_ai_service, sample_documents, claim_context):
        """Test workflow decision making"""
        
        engine = WorkflowDecisionEngine()
        engine.ai_service = mock_ai_service
        
        # Mock AI responses
        mock_ai_service.generate_response.side_effect = [
            "first_notice_of_loss",  # Document classification
            "Slip and fall incident with store negligence"  # Fact extraction
        ]
        
        # Test workflow stage processing
        decision, workflow_state = await engine.process_workflow_stage(
            WorkflowStage.DATA_SUFFICIENCY_CHECK,
            sample_documents,
            claim_context
        )
        
        # Verify workflow decision
        assert decision is not None
        assert workflow_state is not None
        assert decision.recommended_action is not None
        assert decision.next_stage is not None
        assert decision.confidence > 0
    
    @pytest.mark.asyncio
    async def test_audit_compliance_system(self):
        """Test audit trail and compliance checking"""
        
        audit_system = AuditComplianceSystem()
        
        # Test audit event logging
        event = await audit_system.log_audit_event(
            AuditEventType.LIABILITY_CALCULATED,
            "UC05-2024-001",
            {"fault_percentage": 75, "confidence": 0.9},
            user_id="test_user"
        )
        
        # Verify audit event
        assert event is not None
        assert event.event_id is not None
        assert event.claim_id == "UC05-2024-001"
        assert event.data_hash is not None
        
        # Test compliance checking
        compliance_checks = await audit_system.perform_compliance_check(
            "UC05-2024-001", [event]
        )
        
        # Verify compliance results
        assert len(compliance_checks) > 0
        for check in compliance_checks:
            assert check.standard is not None
            assert check.details is not None
            assert check.severity in ["low", "medium", "high", "critical"]
    
    @pytest.mark.asyncio
    async def test_enhanced_document_processing(self, mock_ai_service):
        """Test enhanced document classification and extraction"""
        
        processor = EnhancedDocumentProcessor()
        processor.ai_service = mock_ai_service
        
        # Mock AI responses
        mock_ai_service.generate_response.side_effect = [
            "medical_report|0.95",  # Classification
            '{"injury_severity": {"content": "moderate", "confidence": 0.8}}'  # Evidence extraction
        ]
        
        document = {
            "filename": "medical_report.pdf",
            "text": "Patient diagnosis: wrist sprain, treatment: splint, recovery: 3 weeks"
        }
        
        # Test document processing
        analysis = await processor.process_document(document, "doc_001")
        
        # Verify processing results
        assert analysis is not None
        assert analysis.category == DocumentCategory.MEDICAL_REPORT
        assert analysis.classification_confidence > 0
        assert len(analysis.extracted_evidence) > 0
        assert analysis.quality_score > 0
        assert analysis.summary is not None
    
    @pytest.mark.asyncio
    async def test_edge_case_scenarios(self, mock_ai_service):
        """Test edge cases and error handling"""
        
        analyzer = LiabilityAnalyzer()
        analyzer.ai_service = mock_ai_service
        
        # Test with empty documents
        empty_result = await analyzer.analyze_liability([], {})
        assert empty_result.confidence_score == 0.0
        assert not empty_result.data_sufficiency.can_proceed
        
        # Test with malformed documents
        malformed_docs = [{"invalid": "document"}]
        mock_ai_service.generate_response.return_value = "unknown"
        
        malformed_result = await analyzer.analyze_liability(malformed_docs, {})
        assert malformed_result is not None
        assert malformed_result.confidence_score <= 0.5
    
    @pytest.mark.asyncio
    async def test_performance_benchmarks(self, mock_ai_service, sample_documents, claim_context):
        """Test system performance benchmarks"""
        
        analyzer = LiabilityAnalyzer()
        analyzer.ai_service = mock_ai_service
        
        # Mock fast AI responses
        mock_ai_service.generate_response.return_value = "fast_response"
        
        # Measure processing time
        start_time = datetime.now()
        result = await analyzer.analyze_liability(sample_documents, claim_context)
        end_time = datetime.now()
        
        processing_time = (end_time - start_time).total_seconds()
        
        # Verify performance benchmarks
        assert processing_time < 30  # Should complete within 30 seconds
        assert result is not None
        assert result.confidence_score > 0
        
        # Verify memory efficiency (basic check)
        import sys
        memory_usage = sys.getsizeof(result)
        assert memory_usage < 1024 * 1024  # Less than 1MB for result object

if __name__ == "__main__":
    # Run integration tests
    pytest.main([__file__, "-v", "--asyncio-mode=auto"])
