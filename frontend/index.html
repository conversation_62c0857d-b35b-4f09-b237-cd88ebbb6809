<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rozie AI - Liability Intelligence Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 8px;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
        }

        .logo-text {
            font-size: 24px;
            font-weight: 700;
            color: #1a1a1a;
        }

        .tagline {
            color: #666;
            font-size: 14px;
        }

        .upload-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 32px;
            margin-bottom: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .upload-area {
            border: 2px dashed #667eea;
            border-radius: 12px;
            padding: 48px 24px;
            background: rgba(102, 126, 234, 0.05);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #764ba2;
            background: rgba(118, 75, 162, 0.05);
        }

        .upload-area.dragover {
            border-color: #764ba2;
            background: rgba(118, 75, 162, 0.1);
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 48px;
            color: #667eea;
            margin-bottom: 16px;
        }

        .upload-text {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .upload-subtext {
            color: #666;
            font-size: 14px;
        }

        .file-input {
            display: none;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 16px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .progress-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 16px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        .results-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: none;
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            color: #333;
            margin-bottom: 16px;
        }

        .liability-card {
            background: #f8f9ff;
            border: 1px solid #e0e6ff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
        }

        .fault-allocation {
            display: flex;
            gap: 16px;
            margin: 16px 0;
        }

        .party {
            flex: 1;
            text-align: center;
        }

        .party-label {
            font-weight: 600;
            margin-bottom: 8px;
        }

        .percentage {
            font-size: 24px;
            font-weight: 700;
            color: #667eea;
        }

        .percentage-bar {
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            margin-top: 8px;
            overflow: hidden;
        }

        .percentage-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .reasoning {
            margin-top: 20px;
        }

        .reasoning h4 {
            font-weight: 600;
            margin-bottom: 12px;
            color: #333;
        }

        .reasoning ul {
            list-style: none;
            padding-left: 0;
        }

        .reasoning li {
            padding: 8px 0;
            padding-left: 20px;
            position: relative;
            color: #666;
        }

        .reasoning li:before {
            content: "•";
            color: #667eea;
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        .hidden {
            display: none !important;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">
                <div class="logo-icon">R</div>
                <div>
                    <div class="logo-text">Rozie AI</div>
                    <div class="tagline">Liability Intelligence Platform</div>
                </div>
            </div>
        </div>

        <!-- Upload Section -->
        <div class="upload-section" id="uploadSection">
            <div class="upload-area" id="uploadArea">
                <div class="upload-icon">📄</div>
                <div class="upload-text">Drop your claim documents here</div>
                <div class="upload-subtext">or click to browse files (PDF, TXT, DOC)</div>
                <input type="file" id="fileInput" class="file-input" accept=".pdf,.txt,.doc,.docx" multiple>
                <button class="btn" onclick="document.getElementById('fileInput').click()">
                    Choose Files
                </button>
            </div>
        </div>

        <!-- Progress Section -->
        <div class="progress-section" id="progressSection">
            <div class="section-title">
                <span class="loading"></span>
                Processing Documents...
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="progressText">Uploading files...</div>
        </div>

        <!-- Results Section -->
        <div class="results-section" id="resultsSection">
            <div class="section-title">📊 Analysis Results</div>
            
            <div class="liability-card">
                <h3>🏛️ Liability Decision</h3>
                
                <h4>Fault Allocation</h4>
                <div class="fault-allocation" id="faultAllocation">
                    <!-- Will be populated by JavaScript -->
                </div>

                <div class="reasoning">
                    <h4>Reasoning</h4>
                    <ul id="reasoningList">
                        <!-- Will be populated by JavaScript -->
                    </ul>
                </div>
            </div>

            <div style="margin-top: 20px; text-align: center;">
                <button class="btn" onclick="resetApp()">Analyze Another Document</button>
            </div>
        </div>
    </div>

    <script src="/app.js"></script>
</body>
</html>
