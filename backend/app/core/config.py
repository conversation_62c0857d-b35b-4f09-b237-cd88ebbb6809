"""
Configuration settings for the Zurich Challenge API
"""

from pydantic_settings import BaseSettings
from typing import Optional, List
import os

class Settings(BaseSettings):
    """Application settings"""
    
    # Application
    APP_NAME: str = "Zurich Challenge API"
    VERSION: str = "1.0.0"
    DEBUG: bool = False
    ENVIRONMENT: str = "development"
    
    # API
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days
    
    # Database
    DATABASE_URL: str = "postgresql://zurich:zurich123@localhost:5432/zurich_db"
    
    # Redis
    REDIS_URL: str = "redis://localhost:6379"
    REDIS_PASSWORD: Optional[str] = None
    
    # MinIO S3 Storage
    MINIO_URL: str = "http://localhost:9000"
    MINIO_ACCESS_KEY: str = "minioadmin"
    MINIO_SECRET_KEY: str = "minioadmin123"
    MINIO_BUCKET: str = "zurich-documents"
    
    # AI Services
    OPENAI_API_KEY: Optional[str] = None
    ANTHROPIC_API_KEY: Optional[str] = None
    
    # OCR Settings
    OCR_ENGINE: str = "minicpm-o"  # minicpm-o, paddleocr, tesseract
    OCR_LANGUAGES: List[str] = ["en", "fr"]  # English and French for Canada
    OCR_CONFIDENCE_THRESHOLD: float = 0.8
    
    # Processing
    MAX_FILE_SIZE: int = 50 * 1024 * 1024  # 50MB
    ALLOWED_FILE_TYPES: List[str] = [
        "application/pdf",
        "image/jpeg",
        "image/png", 
        "image/tiff",
        "text/plain"
    ]
    
    # Celery
    CELERY_BROKER_URL: str = "redis://localhost:6379"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379"
    
    # Monitoring
    ENABLE_METRICS: bool = True
    LOG_LEVEL: str = "INFO"
    
    # Use Case Settings
    DEFAULT_USE_CASE: str = "uc05"
    SUPPORTED_USE_CASES: List[str] = [
        "uc01", "uc02", "uc03", "uc04", "uc05",
        "uc06", "uc07", "uc08", "uc09", "uc10",
        "uc11", "uc12", "uc13", "uc14", "uc15",
        "uc16", "uc17", "uc18"
    ]
    
    # Model Paths
    MODELS_DIR: str = "./models"
    DATA_DIR: str = "./data"
    LOGS_DIR: str = "./logs"
    
    # Performance Settings
    MAX_WORKERS: int = 4
    BATCH_SIZE: int = 10
    TIMEOUT_SECONDS: int = 300  # 5 minutes
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# Create settings instance
settings = Settings()

# Ensure directories exist
os.makedirs(settings.MODELS_DIR, exist_ok=True)
os.makedirs(settings.DATA_DIR, exist_ok=True)
os.makedirs(settings.LOGS_DIR, exist_ok=True)
