"""
🎯 UC05: Loss Quantum Calculator
Advanced financial calculation engine for liability claims settlement amounts
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import json
import re
from decimal import Decimal, ROUND_HALF_UP
from datetime import datetime, timedelta

from app.services.ai_service import AIService
from app.core.config import settings

logger = logging.getLogger(__name__)

class DamageCategory(Enum):
    """Categories of damages in liability claims"""
    MEDICAL_EXPENSES = "medical_expenses"
    LOST_WAGES = "lost_wages"
    PROPERTY_DAMAGE = "property_damage"
    PAIN_SUFFERING = "pain_suffering"
    REHABILITATION = "rehabilitation"
    FUTURE_MEDICAL = "future_medical"
    FUTURE_WAGES = "future_wages"
    OTHER_EXPENSES = "other_expenses"

class CalculationMethod(Enum):
    """Methods for calculating damages"""
    ACTUAL_COST = "actual_cost"
    ESTIMATED_COST = "estimated_cost"
    MULTIPLIER_METHOD = "multiplier_method"
    PER_DIEM_METHOD = "per_diem_method"
    COMPARATIVE_METHOD = "comparative_method"

@dataclass
class DamageItem:
    """Individual damage item"""
    category: DamageCategory
    description: str
    amount: Decimal
    calculation_method: CalculationMethod
    supporting_documents: List[str]
    confidence: float
    notes: str

@dataclass
class LossQuantumResult:
    """Complete loss quantum calculation result"""
    total_damages: Decimal
    insured_liability: Decimal
    third_party_liability: Decimal
    settlement_recommendation: Decimal
    damage_breakdown: List[DamageItem]
    fault_percentage: Dict[str, float]
    calculation_confidence: float
    reasoning: str
    recommendations: List[str]

class LossQuantumCalculator:
    """Advanced loss quantum calculation engine"""
    
    def __init__(self):
        self.ai_service = AIService()
        self.damage_multipliers = self._load_damage_multipliers()
        self.calculation_rules = self._load_calculation_rules()
    
    async def calculate_loss_quantum(
        self,
        financial_documents: List[Dict[str, Any]],
        fault_allocation: Dict[str, float],
        claim_context: Optional[Dict[str, Any]] = None
    ) -> LossQuantumResult:
        """
        Calculate total loss quantum and settlement amounts
        
        Args:
            financial_documents: List of financial documents (invoices, receipts, etc.)
            fault_allocation: Fault percentages {"insured": 75, "third_party": 25}
            claim_context: Additional context about the claim
            
        Returns:
            LossQuantumResult with detailed calculations and recommendations
        """
        try:
            logger.info(f"💰 Calculating loss quantum for {len(financial_documents)} documents")
            
            # Step 1: Extract and categorize damages
            damage_items = await self._extract_damage_items(financial_documents)
            
            # Step 2: Validate and verify amounts
            validated_damages = await self._validate_damage_amounts(damage_items, claim_context)
            
            # Step 3: Calculate pain and suffering (if applicable)
            pain_suffering = await self._calculate_pain_suffering(validated_damages, claim_context)
            if pain_suffering:
                validated_damages.append(pain_suffering)
            
            # Step 4: Calculate future damages (if applicable)
            future_damages = await self._calculate_future_damages(validated_damages, claim_context)
            validated_damages.extend(future_damages)
            
            # Step 5: Calculate total damages
            total_damages = sum(item.amount for item in validated_damages)
            
            # Step 6: Apply fault allocation
            insured_fault = fault_allocation.get("insured", 0) / 100
            third_party_fault = fault_allocation.get("third_party", 0) / 100
            
            insured_liability = total_damages * Decimal(str(insured_fault))
            third_party_liability = total_damages * Decimal(str(third_party_fault))
            
            # Step 7: Calculate settlement recommendation
            settlement_recommendation = await self._calculate_settlement_recommendation(
                insured_liability, validated_damages, claim_context
            )
            
            # Step 8: Generate reasoning and recommendations
            reasoning = await self._generate_calculation_reasoning(
                validated_damages, fault_allocation, total_damages
            )
            
            recommendations = await self._generate_settlement_recommendations(
                settlement_recommendation, insured_liability, validated_damages
            )
            
            # Step 9: Calculate confidence score
            confidence = await self._calculate_calculation_confidence(validated_damages)
            
            result = LossQuantumResult(
                total_damages=total_damages.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
                insured_liability=insured_liability.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
                third_party_liability=third_party_liability.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
                settlement_recommendation=settlement_recommendation.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
                damage_breakdown=validated_damages,
                fault_percentage=fault_allocation,
                calculation_confidence=confidence,
                reasoning=reasoning,
                recommendations=recommendations
            )
            
            logger.info(f"✅ Loss quantum calculated: ${total_damages:,.2f} total, ${insured_liability:,.2f} insured liability")
            return result
            
        except Exception as e:
            logger.error(f"❌ Loss quantum calculation failed: {e}")
            raise
    
    async def _extract_damage_items(self, financial_documents: List[Dict[str, Any]]) -> List[DamageItem]:
        """Extract damage items from financial documents"""
        damage_items = []
        
        for doc in financial_documents:
            text = doc.get("text", "")
            filename = doc.get("filename", "")
            
            # Extract monetary amounts and descriptions
            extracted_items = await self._parse_financial_document(text, filename)
            damage_items.extend(extracted_items)
        
        return damage_items
    
    async def _parse_financial_document(self, text: str, filename: str) -> List[DamageItem]:
        """Parse individual financial document"""
        damage_items = []
        
        # Use AI to extract structured financial information
        prompt = f"""
        Analyze this financial document and extract damage/expense items:
        
        Document: {filename}
        Text: {text[:2000]}
        
        For each expense item, extract:
        1. Description of service/item
        2. Amount (numerical value)
        3. Category (medical, property damage, lost wages, etc.)
        4. Date of service/expense
        
        Return as JSON array with structured data.
        """
        
        try:
            response = await self.ai_service.generate_response(prompt)
            expense_data = json.loads(response)
            
            for item in expense_data:
                if isinstance(item, dict) and "amount" in item:
                    # Determine damage category
                    category = self._categorize_damage(item.get("description", ""), item.get("category", ""))
                    
                    # Parse amount
                    amount_str = str(item["amount"]).replace("$", "").replace(",", "")
                    try:
                        amount = Decimal(amount_str)
                    except:
                        continue
                    
                    damage_items.append(DamageItem(
                        category=category,
                        description=item.get("description", "Unknown expense"),
                        amount=amount,
                        calculation_method=CalculationMethod.ACTUAL_COST,
                        supporting_documents=[filename],
                        confidence=0.9,
                        notes=f"Extracted from {filename}"
                    ))
        
        except Exception as e:
            logger.warning(f"⚠️ Financial document parsing failed: {e}")
            
            # Fallback: regex-based extraction
            amount_patterns = [
                r"\$?([\d,]+\.?\d*)",
                r"total[:\s]+\$?([\d,]+\.?\d*)",
                r"amount[:\s]+\$?([\d,]+\.?\d*)"
            ]
            
            for pattern in amount_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                for match in matches:
                    try:
                        amount = Decimal(match.replace(",", ""))
                        if amount > 0:
                            damage_items.append(DamageItem(
                                category=DamageCategory.OTHER_EXPENSES,
                                description=f"Expense from {filename}",
                                amount=amount,
                                calculation_method=CalculationMethod.ACTUAL_COST,
                                supporting_documents=[filename],
                                confidence=0.7,
                                notes="Extracted using regex pattern"
                            ))
                    except:
                        continue
        
        return damage_items
    
    def _categorize_damage(self, description: str, category_hint: str) -> DamageCategory:
        """Categorize damage based on description"""
        desc_lower = description.lower()
        hint_lower = category_hint.lower()
        
        # Medical expenses
        medical_keywords = ["medical", "hospital", "doctor", "physician", "treatment", "surgery", "medication", "therapy"]
        if any(keyword in desc_lower or keyword in hint_lower for keyword in medical_keywords):
            return DamageCategory.MEDICAL_EXPENSES
        
        # Lost wages
        wage_keywords = ["wage", "salary", "income", "lost time", "work", "employment"]
        if any(keyword in desc_lower or keyword in hint_lower for keyword in wage_keywords):
            return DamageCategory.LOST_WAGES
        
        # Property damage
        property_keywords = ["repair", "replacement", "damage", "vehicle", "property", "equipment"]
        if any(keyword in desc_lower or keyword in hint_lower for keyword in property_keywords):
            return DamageCategory.PROPERTY_DAMAGE
        
        # Rehabilitation
        rehab_keywords = ["rehabilitation", "physical therapy", "occupational therapy", "rehab"]
        if any(keyword in desc_lower or keyword in hint_lower for keyword in rehab_keywords):
            return DamageCategory.REHABILITATION
        
        return DamageCategory.OTHER_EXPENSES
    
    async def _validate_damage_amounts(
        self, damage_items: List[DamageItem], claim_context: Optional[Dict[str, Any]]
    ) -> List[DamageItem]:
        """Validate and verify damage amounts"""
        validated_items = []
        
        for item in damage_items:
            # Check for reasonable amounts
            if item.amount <= 0:
                continue
            
            # Check for duplicate items
            is_duplicate = any(
                existing.description == item.description and 
                existing.amount == item.amount
                for existing in validated_items
            )
            
            if is_duplicate:
                continue
            
            # Validate amount reasonableness
            if await self._is_amount_reasonable(item, claim_context):
                validated_items.append(item)
            else:
                # Flag for review but include with lower confidence
                item.confidence *= 0.5
                item.notes += " - Amount flagged for review"
                validated_items.append(item)
        
        return validated_items
    
    async def _is_amount_reasonable(
        self, damage_item: DamageItem, claim_context: Optional[Dict[str, Any]]
    ) -> bool:
        """Check if damage amount is reasonable"""
        
        # Basic reasonableness checks
        if damage_item.amount > Decimal("1000000"):  # $1M threshold
            return False
        
        # Category-specific checks
        if damage_item.category == DamageCategory.MEDICAL_EXPENSES:
            # Medical expenses up to $100k are generally reasonable
            return damage_item.amount <= Decimal("100000")
        elif damage_item.category == DamageCategory.LOST_WAGES:
            # Lost wages up to $50k are generally reasonable
            return damage_item.amount <= Decimal("50000")
        elif damage_item.category == DamageCategory.PROPERTY_DAMAGE:
            # Property damage up to $200k is generally reasonable
            return damage_item.amount <= Decimal("200000")
        
        return True
    
    def _load_damage_multipliers(self) -> Dict[str, float]:
        """Load damage calculation multipliers"""
        return {
            "pain_suffering_mild": 1.5,
            "pain_suffering_moderate": 3.0,
            "pain_suffering_severe": 5.0,
            "future_medical_factor": 1.2,
            "lost_wage_factor": 1.1
        }
    
    def _load_calculation_rules(self) -> Dict[str, Any]:
        """Load calculation rules and thresholds"""
        return {
            "max_pain_suffering_multiplier": 5.0,
            "min_settlement_percentage": 0.7,
            "max_settlement_percentage": 1.2,
            "confidence_threshold": 0.8
        }

    async def _calculate_pain_suffering(
        self, damage_items: List[DamageItem], claim_context: Optional[Dict[str, Any]]
    ) -> Optional[DamageItem]:
        """Calculate pain and suffering damages"""

        # Only calculate if there are medical expenses (indicating injury)
        medical_expenses = [item for item in damage_items if item.category == DamageCategory.MEDICAL_EXPENSES]
        if not medical_expenses:
            return None

        total_medical = sum(item.amount for item in medical_expenses)

        # Determine injury severity from context or medical expenses
        injury_severity = await self._assess_injury_severity(damage_items, claim_context)

        # Apply multiplier based on severity
        multiplier = self.damage_multipliers.get(f"pain_suffering_{injury_severity}", 2.0)
        pain_suffering_amount = total_medical * Decimal(str(multiplier))

        return DamageItem(
            category=DamageCategory.PAIN_SUFFERING,
            description=f"Pain and suffering ({injury_severity} injury)",
            amount=pain_suffering_amount,
            calculation_method=CalculationMethod.MULTIPLIER_METHOD,
            supporting_documents=[item.supporting_documents[0] for item in medical_expenses if item.supporting_documents],
            confidence=0.7,
            notes=f"Calculated using {multiplier}x multiplier for {injury_severity} injury"
        )

    async def _assess_injury_severity(
        self, damage_items: List[DamageItem], claim_context: Optional[Dict[str, Any]]
    ) -> str:
        """Assess injury severity based on medical expenses and context"""

        medical_expenses = [item for item in damage_items if item.category == DamageCategory.MEDICAL_EXPENSES]
        total_medical = sum(item.amount for item in medical_expenses)

        # Basic severity assessment based on medical costs
        if total_medical < Decimal("1000"):
            return "mild"
        elif total_medical < Decimal("10000"):
            return "moderate"
        else:
            return "severe"

    async def _calculate_future_damages(
        self, damage_items: List[DamageItem], claim_context: Optional[Dict[str, Any]]
    ) -> List[DamageItem]:
        """Calculate future damages (medical expenses, lost wages)"""
        future_damages = []

        # Calculate future medical expenses
        medical_items = [item for item in damage_items if item.category == DamageCategory.MEDICAL_EXPENSES]
        if medical_items:
            total_medical = sum(item.amount for item in medical_items)

            # Estimate future medical based on injury type
            future_medical_factor = self.damage_multipliers.get("future_medical_factor", 1.2)
            future_medical_amount = total_medical * Decimal(str(future_medical_factor - 1))  # Additional amount

            if future_medical_amount > 0:
                future_damages.append(DamageItem(
                    category=DamageCategory.FUTURE_MEDICAL,
                    description="Estimated future medical expenses",
                    amount=future_medical_amount,
                    calculation_method=CalculationMethod.ESTIMATED_COST,
                    supporting_documents=[],
                    confidence=0.6,
                    notes=f"Estimated at {future_medical_factor}x current medical expenses"
                ))

        # Calculate future lost wages
        wage_items = [item for item in damage_items if item.category == DamageCategory.LOST_WAGES]
        if wage_items:
            total_wages = sum(item.amount for item in wage_items)

            # Estimate future wage loss based on injury severity
            future_wage_factor = self.damage_multipliers.get("lost_wage_factor", 1.1)
            future_wage_amount = total_wages * Decimal(str(future_wage_factor - 1))  # Additional amount

            if future_wage_amount > 0:
                future_damages.append(DamageItem(
                    category=DamageCategory.FUTURE_WAGES,
                    description="Estimated future lost wages",
                    amount=future_wage_amount,
                    calculation_method=CalculationMethod.ESTIMATED_COST,
                    supporting_documents=[],
                    confidence=0.5,
                    notes=f"Estimated at {future_wage_factor}x current lost wages"
                ))

        return future_damages

    async def _calculate_settlement_recommendation(
        self, insured_liability: Decimal, damage_items: List[DamageItem], claim_context: Optional[Dict[str, Any]]
    ) -> Decimal:
        """Calculate recommended settlement amount"""

        # Base settlement on insured liability
        base_settlement = insured_liability

        # Adjust based on case strength
        case_strength = await self._assess_case_strength(damage_items, claim_context)

        if case_strength == "strong":
            # Settle at full liability amount
            settlement_factor = 1.0
        elif case_strength == "moderate":
            # Settle at 85% of liability
            settlement_factor = 0.85
        else:  # weak
            # Settle at 70% of liability
            settlement_factor = 0.7

        recommended_settlement = base_settlement * Decimal(str(settlement_factor))

        # Apply bounds
        min_settlement = insured_liability * Decimal(str(self.calculation_rules["min_settlement_percentage"]))
        max_settlement = insured_liability * Decimal(str(self.calculation_rules["max_settlement_percentage"]))

        return max(min_settlement, min(recommended_settlement, max_settlement))

    async def _assess_case_strength(
        self, damage_items: List[DamageItem], claim_context: Optional[Dict[str, Any]]
    ) -> str:
        """Assess the strength of the case for settlement purposes"""

        # Factors that strengthen the case
        strength_score = 0.5  # Base score

        # Well-documented damages
        documented_items = [item for item in damage_items if item.supporting_documents]
        if len(documented_items) / len(damage_items) > 0.8:
            strength_score += 0.2

        # High confidence in damage calculations
        avg_confidence = sum(item.confidence for item in damage_items) / len(damage_items)
        if avg_confidence > 0.8:
            strength_score += 0.2

        # Reasonable damage amounts
        reasonable_items = [item for item in damage_items if item.confidence > 0.7]
        if len(reasonable_items) / len(damage_items) > 0.9:
            strength_score += 0.1

        # Categorize strength
        if strength_score > 0.8:
            return "strong"
        elif strength_score > 0.6:
            return "moderate"
        else:
            return "weak"

    async def _generate_calculation_reasoning(
        self, damage_items: List[DamageItem], fault_allocation: Dict[str, float], total_damages: Decimal
    ) -> str:
        """Generate reasoning for the calculation"""

        reasoning_parts = []

        # Damage breakdown
        reasoning_parts.append("DAMAGE CALCULATION BREAKDOWN:")

        category_totals = {}
        for item in damage_items:
            category = item.category.value.replace('_', ' ').title()
            if category not in category_totals:
                category_totals[category] = Decimal('0')
            category_totals[category] += item.amount

        for category, amount in category_totals.items():
            reasoning_parts.append(f"- {category}: ${amount:,.2f}")

        reasoning_parts.append(f"\nTotal Damages: ${total_damages:,.2f}")

        # Fault allocation
        reasoning_parts.append("\nFAULT ALLOCATION:")
        for party, percentage in fault_allocation.items():
            party_liability = total_damages * Decimal(str(percentage / 100))
            reasoning_parts.append(f"- {party.title()}: {percentage}% (${party_liability:,.2f})")

        # Calculation methods
        reasoning_parts.append("\nCALCULATION METHODS:")
        method_counts = {}
        for item in damage_items:
            method = item.calculation_method.value.replace('_', ' ').title()
            method_counts[method] = method_counts.get(method, 0) + 1

        for method, count in method_counts.items():
            reasoning_parts.append(f"- {method}: {count} items")

        return "\n".join(reasoning_parts)

    async def _generate_settlement_recommendations(
        self, settlement_amount: Decimal, insured_liability: Decimal, damage_items: List[DamageItem]
    ) -> List[str]:
        """Generate settlement recommendations"""

        recommendations = []

        # Settlement amount recommendation
        settlement_percentage = (settlement_amount / insured_liability * 100) if insured_liability > 0 else 0
        recommendations.append(f"Recommend settlement at ${settlement_amount:,.2f} ({settlement_percentage:.1f}% of insured liability)")

        # Documentation recommendations
        undocumented_items = [item for item in damage_items if not item.supporting_documents]
        if undocumented_items:
            recommendations.append(f"Request additional documentation for {len(undocumented_items)} damage items")

        # High-value item recommendations
        high_value_items = [item for item in damage_items if item.amount > Decimal("10000")]
        if high_value_items:
            recommendations.append("Consider independent verification for high-value damage items")

        # Future damages recommendations
        future_items = [item for item in damage_items if item.category in [DamageCategory.FUTURE_MEDICAL, DamageCategory.FUTURE_WAGES]]
        if future_items:
            recommendations.append("Monitor claimant for future damage development")

        # Payment structure recommendations
        if settlement_amount > Decimal("50000"):
            recommendations.append("Consider structured settlement for large amounts")

        return recommendations

    async def _calculate_calculation_confidence(self, damage_items: List[DamageItem]) -> float:
        """Calculate overall confidence in the calculation"""

        if not damage_items:
            return 0.0

        # Average confidence of all items
        avg_confidence = sum(item.confidence for item in damage_items) / len(damage_items)

        # Adjust based on documentation
        documented_ratio = len([item for item in damage_items if item.supporting_documents]) / len(damage_items)

        # Adjust based on calculation methods
        actual_cost_ratio = len([item for item in damage_items if item.calculation_method == CalculationMethod.ACTUAL_COST]) / len(damage_items)

        # Weighted confidence score
        confidence = (
            0.5 * avg_confidence +
            0.3 * documented_ratio +
            0.2 * actual_cost_ratio
        )

        return round(confidence, 2)
