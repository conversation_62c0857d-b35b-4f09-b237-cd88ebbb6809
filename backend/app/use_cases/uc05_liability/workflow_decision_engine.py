"""
🎯 UC05: Workflow Decision Engine
Intelligent decision-making system for claims processing workflow automation
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import json
from datetime import datetime, timedelta

from app.services.ai_service import AIService
from app.use_cases.uc05_liability.insufficient_data_handler import (
    InsufficientDataHandler, DataSufficiencyAssessment, DataSufficiencyLevel
)
from app.use_cases.uc05_liability.legal_precedent_engine import (
    LegalPrecedentEngine, LegalAnalysis, LiabilityType, Province
)

logger = logging.getLogger(__name__)

class WorkflowStage(Enum):
    """Workflow stages for claims processing"""
    DOCUMENT_INTAKE = "document_intake"
    DATA_SUFFICIENCY_CHECK = "data_sufficiency_check"
    LEGAL_ANALYSIS = "legal_analysis"
    LIABILITY_ASSESSMENT = "liability_assessment"
    COVERAGE_DETERMINATION = "coverage_determination"
    LOSS_QUANTUM_CALCULATION = "loss_quantum_calculation"
    DECISION_FINALIZATION = "decision_finalization"
    COMMUNICATION_GENERATION = "communication_generation"
    QUALITY_REVIEW = "quality_review"
    CASE_CLOSURE = "case_closure"

class DecisionPoint(Enum):
    """Key decision points in the workflow"""
    IS_DATA_SUFFICIENT = "is_data_sufficient"
    CAN_PROCEED_WITH_ANALYSIS = "can_proceed_with_analysis"
    IS_COVERAGE_CLEAR = "is_coverage_clear"
    IS_LIABILITY_CLEAR = "is_liability_clear"
    REQUIRES_LEGAL_REVIEW = "requires_legal_review"
    REQUIRES_MANUAL_REVIEW = "requires_manual_review"
    CAN_AUTO_SETTLE = "can_auto_settle"

class WorkflowAction(Enum):
    """Possible workflow actions"""
    PROCEED = "proceed"
    PAUSE = "pause"
    REQUEST_DOCUMENTS = "request_documents"
    ESCALATE_TO_LEGAL = "escalate_to_legal"
    ESCALATE_TO_SENIOR = "escalate_to_senior"
    GENERATE_SETTLEMENT = "generate_settlement"
    DENY_CLAIM = "deny_claim"
    CLOSE_CASE = "close_case"

@dataclass
class WorkflowDecision:
    """Workflow decision result"""
    decision_point: DecisionPoint
    decision: bool
    confidence: float
    reasoning: str
    recommended_action: WorkflowAction
    next_stage: WorkflowStage
    conditions: List[str]

@dataclass
class WorkflowState:
    """Current workflow state"""
    current_stage: WorkflowStage
    completed_stages: List[WorkflowStage]
    pending_actions: List[WorkflowAction]
    data_sufficiency: Optional[DataSufficiencyAssessment]
    legal_analysis: Optional[LegalAnalysis]
    workflow_decisions: List[WorkflowDecision]
    estimated_completion: Optional[datetime]

class WorkflowDecisionEngine:
    """Advanced workflow decision engine for claims processing"""
    
    def __init__(self):
        self.ai_service = AIService()
        self.data_handler = InsufficientDataHandler()
        self.legal_engine = LegalPrecedentEngine()
        self.decision_rules = self._load_decision_rules()
        self.escalation_thresholds = self._load_escalation_thresholds()
    
    async def process_workflow_stage(
        self,
        current_stage: WorkflowStage,
        documents: List[Dict[str, Any]],
        claim_context: Dict[str, Any],
        workflow_state: Optional[WorkflowState] = None
    ) -> Tuple[WorkflowDecision, WorkflowState]:
        """
        Process a workflow stage and make intelligent decisions
        
        Args:
            current_stage: Current workflow stage
            documents: Available documents
            claim_context: Claim context information
            workflow_state: Current workflow state
            
        Returns:
            Tuple of (WorkflowDecision, updated WorkflowState)
        """
        try:
            logger.info(f"🔄 Processing workflow stage: {current_stage.value}")
            
            # Initialize workflow state if not provided
            if workflow_state is None:
                workflow_state = WorkflowState(
                    current_stage=current_stage,
                    completed_stages=[],
                    pending_actions=[],
                    data_sufficiency=None,
                    legal_analysis=None,
                    workflow_decisions=[],
                    estimated_completion=None
                )
            
            # Process stage-specific logic
            if current_stage == WorkflowStage.DATA_SUFFICIENCY_CHECK:
                decision, updated_state = await self._process_data_sufficiency_check(
                    documents, claim_context, workflow_state
                )
            elif current_stage == WorkflowStage.LEGAL_ANALYSIS:
                decision, updated_state = await self._process_legal_analysis(
                    documents, claim_context, workflow_state
                )
            elif current_stage == WorkflowStage.LIABILITY_ASSESSMENT:
                decision, updated_state = await self._process_liability_assessment(
                    documents, claim_context, workflow_state
                )
            elif current_stage == WorkflowStage.COVERAGE_DETERMINATION:
                decision, updated_state = await self._process_coverage_determination(
                    documents, claim_context, workflow_state
                )
            else:
                decision, updated_state = await self._process_generic_stage(
                    current_stage, documents, claim_context, workflow_state
                )
            
            # Update workflow state
            updated_state.workflow_decisions.append(decision)
            if current_stage not in updated_state.completed_stages:
                updated_state.completed_stages.append(current_stage)
            
            # Calculate estimated completion
            updated_state.estimated_completion = await self._calculate_estimated_completion(
                updated_state
            )
            
            logger.info(f"✅ Workflow decision: {decision.recommended_action.value}")
            return decision, updated_state
            
        except Exception as e:
            logger.error(f"❌ Workflow processing failed: {e}")
            raise
    
    async def _process_data_sufficiency_check(
        self,
        documents: List[Dict[str, Any]],
        claim_context: Dict[str, Any],
        workflow_state: WorkflowState
    ) -> Tuple[WorkflowDecision, WorkflowState]:
        """Process data sufficiency check stage"""
        
        # Assess data sufficiency
        data_assessment = await self.data_handler.assess_data_sufficiency(
            documents, claim_context
        )
        workflow_state.data_sufficiency = data_assessment
        
        # Make decision based on sufficiency level
        if data_assessment.level == DataSufficiencyLevel.COMPLETE:
            decision = WorkflowDecision(
                decision_point=DecisionPoint.IS_DATA_SUFFICIENT,
                decision=True,
                confidence=data_assessment.confidence_score,
                reasoning="All required documents present and complete",
                recommended_action=WorkflowAction.PROCEED,
                next_stage=WorkflowStage.LEGAL_ANALYSIS,
                conditions=[]
            )
        elif data_assessment.level == DataSufficiencyLevel.SUFFICIENT:
            decision = WorkflowDecision(
                decision_point=DecisionPoint.CAN_PROCEED_WITH_ANALYSIS,
                decision=True,
                confidence=data_assessment.confidence_score,
                reasoning="Sufficient documentation to proceed with preliminary analysis",
                recommended_action=WorkflowAction.PROCEED,
                next_stage=WorkflowStage.LEGAL_ANALYSIS,
                conditions=["Request additional documents in parallel"]
            )
        else:
            decision = WorkflowDecision(
                decision_point=DecisionPoint.IS_DATA_SUFFICIENT,
                decision=False,
                confidence=data_assessment.confidence_score,
                reasoning=f"Insufficient data: {len(data_assessment.missing_documents)} critical documents missing",
                recommended_action=WorkflowAction.REQUEST_DOCUMENTS,
                next_stage=WorkflowStage.DOCUMENT_INTAKE,
                conditions=[f"Missing: {', '.join([doc.document_type.value for doc in data_assessment.missing_documents])}"]
            )
        
        # Add pending actions for document requests
        if data_assessment.missing_documents:
            workflow_state.pending_actions.append(WorkflowAction.REQUEST_DOCUMENTS)
        
        return decision, workflow_state
    
    async def _process_legal_analysis(
        self,
        documents: List[Dict[str, Any]],
        claim_context: Dict[str, Any],
        workflow_state: WorkflowState
    ) -> Tuple[WorkflowDecision, WorkflowState]:
        """Process legal analysis stage"""
        
        # Extract case facts for legal analysis
        case_facts = await self._extract_case_facts(documents)
        
        # Determine liability type and province
        liability_type = self._determine_liability_type(claim_context)
        province = self._determine_province(claim_context)
        
        # Perform legal analysis
        legal_analysis = await self.legal_engine.analyze_legal_precedents(
            case_facts, liability_type, province, documents
        )
        workflow_state.legal_analysis = legal_analysis
        
        # Make decision based on legal analysis confidence
        if legal_analysis.confidence_score > 0.8:
            decision = WorkflowDecision(
                decision_point=DecisionPoint.IS_LIABILITY_CLEAR,
                decision=True,
                confidence=legal_analysis.confidence_score,
                reasoning="Strong legal precedent support with clear liability determination",
                recommended_action=WorkflowAction.PROCEED,
                next_stage=WorkflowStage.LIABILITY_ASSESSMENT,
                conditions=[]
            )
        elif legal_analysis.confidence_score > 0.6:
            decision = WorkflowDecision(
                decision_point=DecisionPoint.IS_LIABILITY_CLEAR,
                decision=True,
                confidence=legal_analysis.confidence_score,
                reasoning="Moderate legal precedent support - proceed with caution",
                recommended_action=WorkflowAction.PROCEED,
                next_stage=WorkflowStage.LIABILITY_ASSESSMENT,
                conditions=["Consider legal review for complex aspects"]
            )
        else:
            decision = WorkflowDecision(
                decision_point=DecisionPoint.REQUIRES_LEGAL_REVIEW,
                decision=True,
                confidence=legal_analysis.confidence_score,
                reasoning="Low confidence in legal analysis - requires expert review",
                recommended_action=WorkflowAction.ESCALATE_TO_LEGAL,
                next_stage=WorkflowStage.QUALITY_REVIEW,
                conditions=["Legal counsel consultation required"]
            )
        
        return decision, workflow_state
    
    async def _process_liability_assessment(
        self,
        documents: List[Dict[str, Any]],
        claim_context: Dict[str, Any],
        workflow_state: WorkflowState
    ) -> Tuple[WorkflowDecision, WorkflowState]:
        """Process liability assessment stage"""
        
        # Check if we have legal analysis
        if not workflow_state.legal_analysis:
            decision = WorkflowDecision(
                decision_point=DecisionPoint.CAN_PROCEED_WITH_ANALYSIS,
                decision=False,
                confidence=0.0,
                reasoning="Legal analysis required before liability assessment",
                recommended_action=WorkflowAction.ESCALATE_TO_SENIOR,
                next_stage=WorkflowStage.LEGAL_ANALYSIS,
                conditions=["Complete legal analysis first"]
            )
            return decision, workflow_state
        
        # Assess liability based on legal analysis and precedents
        liability_confidence = workflow_state.legal_analysis.confidence_score
        
        # Check for clear liability determination
        if liability_confidence > 0.8:
            decision = WorkflowDecision(
                decision_point=DecisionPoint.IS_LIABILITY_CLEAR,
                decision=True,
                confidence=liability_confidence,
                reasoning="Clear liability determination based on strong legal precedents",
                recommended_action=WorkflowAction.PROCEED,
                next_stage=WorkflowStage.COVERAGE_DETERMINATION,
                conditions=[]
            )
        elif liability_confidence > 0.6:
            decision = WorkflowDecision(
                decision_point=DecisionPoint.REQUIRES_MANUAL_REVIEW,
                decision=True,
                confidence=liability_confidence,
                reasoning="Moderate liability confidence - manual review recommended",
                recommended_action=WorkflowAction.PROCEED,
                next_stage=WorkflowStage.COVERAGE_DETERMINATION,
                conditions=["Flag for senior adjuster review"]
            )
        else:
            decision = WorkflowDecision(
                decision_point=DecisionPoint.REQUIRES_LEGAL_REVIEW,
                decision=True,
                confidence=liability_confidence,
                reasoning="Complex liability determination requires expert analysis",
                recommended_action=WorkflowAction.ESCALATE_TO_LEGAL,
                next_stage=WorkflowStage.QUALITY_REVIEW,
                conditions=["Legal and senior management review required"]
            )
        
        return decision, workflow_state

    async def _process_coverage_determination(
        self,
        documents: List[Dict[str, Any]],
        claim_context: Dict[str, Any],
        workflow_state: WorkflowState
    ) -> Tuple[WorkflowDecision, WorkflowState]:
        """Process coverage determination stage"""

        # Check if we have legal analysis with coverage information
        coverage_status = "unknown"
        coverage_confidence = 0.5

        if workflow_state.legal_analysis:
            coverage_analysis = workflow_state.legal_analysis.coverage_analysis
            coverage_status = coverage_analysis.get("coverage_determination", "unknown")
            coverage_confidence = workflow_state.legal_analysis.confidence_score

        # Make decision based on coverage status
        if coverage_status == "covered":
            decision = WorkflowDecision(
                decision_point=DecisionPoint.IS_COVERAGE_CLEAR,
                decision=True,
                confidence=coverage_confidence,
                reasoning="Clear coverage under applicable policy clauses",
                recommended_action=WorkflowAction.PROCEED,
                next_stage=WorkflowStage.LOSS_QUANTUM_CALCULATION,
                conditions=[]
            )
        elif coverage_status == "not_covered":
            decision = WorkflowDecision(
                decision_point=DecisionPoint.IS_COVERAGE_CLEAR,
                decision=True,
                confidence=coverage_confidence,
                reasoning="Clear exclusion from coverage - claim should be denied",
                recommended_action=WorkflowAction.DENY_CLAIM,
                next_stage=WorkflowStage.COMMUNICATION_GENERATION,
                conditions=["Generate denial letter with detailed reasoning"]
            )
        elif coverage_status == "coverage_disputed":
            decision = WorkflowDecision(
                decision_point=DecisionPoint.REQUIRES_LEGAL_REVIEW,
                decision=True,
                confidence=coverage_confidence,
                reasoning="Coverage disputed due to potential exclusions",
                recommended_action=WorkflowAction.ESCALATE_TO_LEGAL,
                next_stage=WorkflowStage.QUALITY_REVIEW,
                conditions=["Legal review of coverage exclusions required"]
            )
        else:
            decision = WorkflowDecision(
                decision_point=DecisionPoint.IS_COVERAGE_CLEAR,
                decision=False,
                confidence=coverage_confidence,
                reasoning="Coverage determination unclear - requires additional analysis",
                recommended_action=WorkflowAction.ESCALATE_TO_SENIOR,
                next_stage=WorkflowStage.QUALITY_REVIEW,
                conditions=["Senior adjuster review of policy terms required"]
            )

        return decision, workflow_state

    async def _process_generic_stage(
        self,
        current_stage: WorkflowStage,
        documents: List[Dict[str, Any]],
        claim_context: Dict[str, Any],
        workflow_state: WorkflowState
    ) -> Tuple[WorkflowDecision, WorkflowState]:
        """Process generic workflow stages"""

        # Default processing for other stages
        decision = WorkflowDecision(
            decision_point=DecisionPoint.CAN_PROCEED_WITH_ANALYSIS,
            decision=True,
            confidence=0.8,
            reasoning=f"Standard processing for {current_stage.value}",
            recommended_action=WorkflowAction.PROCEED,
            next_stage=self._get_next_stage(current_stage),
            conditions=[]
        )

        return decision, workflow_state

    def _get_next_stage(self, current_stage: WorkflowStage) -> WorkflowStage:
        """Get the next workflow stage"""
        stage_sequence = [
            WorkflowStage.DOCUMENT_INTAKE,
            WorkflowStage.DATA_SUFFICIENCY_CHECK,
            WorkflowStage.LEGAL_ANALYSIS,
            WorkflowStage.LIABILITY_ASSESSMENT,
            WorkflowStage.COVERAGE_DETERMINATION,
            WorkflowStage.LOSS_QUANTUM_CALCULATION,
            WorkflowStage.DECISION_FINALIZATION,
            WorkflowStage.COMMUNICATION_GENERATION,
            WorkflowStage.QUALITY_REVIEW,
            WorkflowStage.CASE_CLOSURE
        ]

        try:
            current_index = stage_sequence.index(current_stage)
            if current_index < len(stage_sequence) - 1:
                return stage_sequence[current_index + 1]
            else:
                return WorkflowStage.CASE_CLOSURE
        except ValueError:
            return WorkflowStage.QUALITY_REVIEW

    def _load_decision_rules(self) -> Dict[str, Any]:
        """Load decision rules for workflow processing"""
        return {
            "data_sufficiency": {
                "minimum_documents": 2,
                "required_document_types": ["incident_report", "policy_document"],
                "confidence_threshold": 0.7
            },
            "legal_analysis": {
                "precedent_confidence_threshold": 0.8,
                "escalation_threshold": 0.6,
                "review_threshold": 0.4
            },
            "liability_assessment": {
                "clear_liability_threshold": 0.8,
                "manual_review_threshold": 0.6,
                "legal_review_threshold": 0.4
            },
            "coverage_determination": {
                "coverage_confidence_threshold": 0.8,
                "dispute_threshold": 0.6,
                "escalation_threshold": 0.4
            }
        }

    def _load_escalation_thresholds(self) -> Dict[str, Any]:
        """Load escalation thresholds for workflow decisions"""
        return {
            "confidence_thresholds": {
                "auto_approve": 0.9,
                "senior_review": 0.7,
                "legal_review": 0.5,
                "manual_processing": 0.3
            },
            "complexity_thresholds": {
                "simple": 0.8,
                "moderate": 0.6,
                "complex": 0.4,
                "very_complex": 0.2
            },
            "amount_thresholds": {
                "auto_approve_limit": 10000,
                "senior_review_limit": 50000,
                "legal_review_limit": 100000
            },
            "time_limits": {
                "standard_processing_days": 5,
                "expedited_processing_days": 2,
                "complex_processing_days": 10
            }
        }

    async def _calculate_estimated_completion(self, workflow_state: WorkflowState) -> datetime:
        """Calculate estimated completion time for the workflow"""
        from datetime import datetime, timedelta

        # Base processing time per stage (in hours)
        stage_times = {
            WorkflowStage.DOCUMENT_INTAKE: 0.5,
            WorkflowStage.DATA_SUFFICIENCY_CHECK: 1.0,
            WorkflowStage.LEGAL_ANALYSIS: 4.0,
            WorkflowStage.LIABILITY_ASSESSMENT: 2.0,
            WorkflowStage.COVERAGE_DETERMINATION: 1.5,
            WorkflowStage.LOSS_QUANTUM_CALCULATION: 2.0,
            WorkflowStage.DECISION_FINALIZATION: 1.0,
            WorkflowStage.COMMUNICATION_GENERATION: 0.5,
            WorkflowStage.QUALITY_REVIEW: 2.0,
            WorkflowStage.CASE_CLOSURE: 0.5
        }

        # Calculate remaining time based on completed stages
        remaining_hours = 0
        for stage in WorkflowStage:
            if stage not in workflow_state.completed_stages:
                base_time = stage_times.get(stage, 1.0)

                # Apply complexity multiplier
                complexity_multiplier = 1.0
                if workflow_state.legal_analysis:
                    if workflow_state.legal_analysis.confidence_score < 0.5:
                        complexity_multiplier = 2.0
                    elif workflow_state.legal_analysis.confidence_score < 0.7:
                        complexity_multiplier = 1.5

                remaining_hours += base_time * complexity_multiplier

        # Add current time
        estimated_completion = datetime.now() + timedelta(hours=remaining_hours)
        return estimated_completion

    async def _extract_case_facts(self, documents: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Extract case facts from documents for legal analysis"""
        case_facts = {
            "incident_type": "unknown",
            "parties_involved": [],
            "incident_date": None,
            "incident_location": None,
            "damages": [],
            "witnesses": []
        }

        # Extract facts from all documents
        for doc in documents:
            text = doc.get("text", "")

            # Extract incident type
            if "collision" in text.lower() or "accident" in text.lower():
                case_facts["incident_type"] = "vehicle_collision"
            elif "slip" in text.lower() or "fall" in text.lower():
                case_facts["incident_type"] = "slip_and_fall"

            # Extract parties (simplified)
            import re
            names = re.findall(r"\b[A-Z][a-z]+ [A-Z][a-z]+\b", text)
            case_facts["parties_involved"].extend(names[:5])  # Limit to 5 names

            # Extract dates
            dates = re.findall(r"\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b", text)
            if dates and not case_facts["incident_date"]:
                case_facts["incident_date"] = dates[0]

        return case_facts

    def _determine_liability_type(self, claim_context: Dict[str, Any]) -> str:
        """Determine liability type from claim context"""
        # Default to motor vehicle liability for demo
        return "motor_vehicle"

    def _determine_province(self, claim_context: Dict[str, Any]) -> str:
        """Determine province from claim context"""
        # Default to Ontario for demo
        return "ontario"
