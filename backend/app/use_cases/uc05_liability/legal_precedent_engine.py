"""
🎯 UC05: Legal Precedent Engine
Canadian legal precedent database and case matching system for liability decisions
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import json
import re
from datetime import datetime

from app.services.ai_service import AIService
from app.core.config import settings

logger = logging.getLogger(__name__)

class Province(Enum):
    """Canadian provinces and territories"""
    AB = "Alberta"
    BC = "British Columbia"
    MB = "Manitoba"
    NB = "New Brunswick"
    NL = "Newfoundland and Labrador"
    NS = "Nova Scotia"
    NT = "Northwest Territories"
    NU = "Nunavut"
    ON = "Ontario"
    PE = "Prince Edward Island"
    QC = "Quebec"
    SK = "Saskatchewan"
    YT = "Yukon"

class LiabilityType(Enum):
    """Types of liability claims"""
    PREMISES_LIABILITY = "premises_liability"
    MOTOR_VEHICLE = "motor_vehicle"
    PRODUCT_LIABILITY = "product_liability"
    PROFESSIONAL_LIABILITY = "professional_liability"
    GENERAL_LIABILITY = "general_liability"

@dataclass
class LegalPrecedent:
    """Legal precedent case information"""
    case_id: str
    case_name: str
    year: int
    province: Province
    liability_type: LiabilityType
    fault_allocation: Dict[str, float]  # {"plaintiff": 25, "defendant": 75}
    key_facts: List[str]
    legal_principle: str
    citation: str
    relevance_score: float

@dataclass
class CoverageClause:
    """Insurance coverage clause information"""
    clause_id: str
    clause_type: str  # "premises_liability", "exclusion", "limit"
    title: str
    text: str
    interpretation: str
    applicable_scenarios: List[str]

@dataclass
class LegalAnalysis:
    """Legal analysis result"""
    applicable_precedents: List[LegalPrecedent]
    coverage_analysis: Dict[str, Any]
    legal_reasoning: str
    confidence_score: float
    recommendations: List[str]

class LegalPrecedentEngine:
    """Advanced legal precedent matching and analysis system"""
    
    def __init__(self):
        self.ai_service = AIService()
        self.precedent_database = self._load_precedent_database()
        self.coverage_clauses = self._load_coverage_clauses()
        self.legal_principles = self._load_legal_principles()
    
    async def analyze_legal_precedents(
        self,
        case_facts: List[str],
        liability_type: LiabilityType,
        province: Province,
        policy_documents: Optional[List[Dict[str, Any]]] = None
    ) -> LegalAnalysis:
        """
        Analyze legal precedents for a liability case
        
        Args:
            case_facts: List of key facts from the case
            liability_type: Type of liability claim
            province: Canadian province for jurisdiction
            policy_documents: Insurance policy documents
            
        Returns:
            LegalAnalysis with applicable precedents and coverage analysis
        """
        try:
            logger.info(f"🏛️ Analyzing legal precedents for {liability_type.value} in {province.value}")
            
            # Step 1: Find applicable precedents
            applicable_precedents = await self._find_applicable_precedents(
                case_facts, liability_type, province
            )
            
            # Step 2: Analyze coverage clauses
            coverage_analysis = await self._analyze_coverage_clauses(
                case_facts, liability_type, policy_documents
            )
            
            # Step 3: Generate legal reasoning
            legal_reasoning = await self._generate_legal_reasoning(
                case_facts, applicable_precedents, coverage_analysis
            )
            
            # Step 4: Calculate confidence score
            confidence_score = await self._calculate_legal_confidence(
                applicable_precedents, coverage_analysis
            )
            
            # Step 5: Generate recommendations
            recommendations = await self._generate_legal_recommendations(
                applicable_precedents, coverage_analysis, confidence_score
            )
            
            analysis = LegalAnalysis(
                applicable_precedents=applicable_precedents,
                coverage_analysis=coverage_analysis,
                legal_reasoning=legal_reasoning,
                confidence_score=confidence_score,
                recommendations=recommendations
            )
            
            logger.info(f"✅ Legal analysis complete: {len(applicable_precedents)} precedents found")
            return analysis
            
        except Exception as e:
            logger.error(f"❌ Legal precedent analysis failed: {e}")
            raise
    
    def _load_precedent_database(self) -> List[LegalPrecedent]:
        """Load Canadian legal precedent database"""
        # In production, this would load from a comprehensive legal database
        return [
            LegalPrecedent(
                case_id="2023-CA-512",
                case_name="Thompson v. Metro Grocery Ltd.",
                year=2023,
                province=Province.ON,
                liability_type=LiabilityType.PREMISES_LIABILITY,
                fault_allocation={"plaintiff": 20, "defendant": 80},
                key_facts=[
                    "Slip and fall on wet floor",
                    "No warning signs posted",
                    "Store employee aware of spill",
                    "Plaintiff wearing appropriate footwear"
                ],
                legal_principle="Occupier's liability for known hazards",
                citation="2023 ONCA 512",
                relevance_score=0.95
            ),
            LegalPrecedent(
                case_id="2022-BC-341",
                case_name="Wilson v. Pacific Shopping Centre",
                year=2022,
                province=Province.BC,
                liability_type=LiabilityType.PREMISES_LIABILITY,
                fault_allocation={"plaintiff": 30, "defendant": 70},
                key_facts=[
                    "Slip on icy walkway",
                    "Inadequate snow removal",
                    "Plaintiff aware of weather conditions",
                    "No alternative route provided"
                ],
                legal_principle="Reasonable care in winter maintenance",
                citation="2022 BCSC 341",
                relevance_score=0.85
            ),
            LegalPrecedent(
                case_id="2023-AB-156",
                case_name="Johnson v. City Centre Mall",
                year=2023,
                province=Province.AB,
                liability_type=LiabilityType.PREMISES_LIABILITY,
                fault_allocation={"plaintiff": 0, "defendant": 100},
                key_facts=[
                    "Falling merchandise from shelf",
                    "Improper stacking by staff",
                    "No customer fault identified",
                    "Clear negligence in storage"
                ],
                legal_principle="Strict liability for falling objects",
                citation="2023 ABQB 156",
                relevance_score=0.90
            ),
            LegalPrecedent(
                case_id="2022-ON-789",
                case_name="Davis v. Restaurant Group Inc.",
                year=2022,
                province=Province.ON,
                liability_type=LiabilityType.PREMISES_LIABILITY,
                fault_allocation={"plaintiff": 40, "defendant": 60},
                key_facts=[
                    "Trip over uneven flooring",
                    "Poor lighting conditions",
                    "Plaintiff using mobile phone",
                    "Hazard not clearly marked"
                ],
                legal_principle="Contributory negligence in premises liability",
                citation="2022 ONSC 789",
                relevance_score=0.80
            )
        ]
    
    def _load_coverage_clauses(self) -> List[CoverageClause]:
        """Load insurance coverage clause library"""
        return [
            CoverageClause(
                clause_id="CGL-001",
                clause_type="premises_liability",
                title="Premises and Operations Coverage",
                text="The insurer will pay those sums that the insured becomes legally obligated to pay as damages because of bodily injury or property damage to which this insurance applies, caused by an occurrence that takes place in the coverage territory during the policy period.",
                interpretation="Covers liability arising from ownership, maintenance, or use of premises",
                applicable_scenarios=["slip and fall", "falling objects", "inadequate maintenance"]
            ),
            CoverageClause(
                clause_id="CGL-002",
                clause_type="exclusion",
                title="Expected or Intended Injury Exclusion",
                text="This insurance does not apply to bodily injury or property damage expected or intended from the standpoint of the insured.",
                interpretation="Excludes coverage for intentional acts",
                applicable_scenarios=["intentional harm", "deliberate damage"]
            ),
            CoverageClause(
                clause_id="CGL-003",
                clause_type="limit",
                title="Per Occurrence Limit",
                text="The most we will pay for the sum of all damages because of all bodily injury and property damage arising out of any one occurrence is the Per Occurrence Limit shown in the Declarations.",
                interpretation="Sets maximum payout per incident",
                applicable_scenarios=["multiple injuries", "large claims"]
            )
        ]
    
    def _load_legal_principles(self) -> Dict[str, str]:
        """Load Canadian legal principles"""
        return {
            "occupiers_liability": "Occupiers owe a duty of care to ensure premises are reasonably safe",
            "contributory_negligence": "Damages may be reduced based on plaintiff's contribution to the incident",
            "reasonable_care": "Standard of care expected from a reasonable person in similar circumstances",
            "foreseeability": "Liability exists if harm was reasonably foreseeable",
            "causation": "Must establish both factual and legal causation between act and harm"
        }

    async def _find_applicable_precedents(
        self,
        case_facts: List[str],
        liability_type: LiabilityType,
        province: Province
    ) -> List[LegalPrecedent]:
        """Find applicable legal precedents based on case facts"""
        applicable_precedents = []

        # Filter by liability type and province
        relevant_precedents = [
            p for p in self.precedent_database
            if p.liability_type == liability_type
        ]

        # Score precedents based on fact similarity
        for precedent in relevant_precedents:
            similarity_score = await self._calculate_fact_similarity(
                case_facts, precedent.key_facts
            )

            # Adjust score based on jurisdiction
            if precedent.province == province:
                similarity_score *= 1.2  # Same province bonus
            elif precedent.province in [Province.ON, Province.BC]:  # Major jurisdictions
                similarity_score *= 1.1

            # Include if similarity is above threshold
            if similarity_score > 0.6:
                precedent.relevance_score = similarity_score
                applicable_precedents.append(precedent)

        # Sort by relevance score
        applicable_precedents.sort(key=lambda p: p.relevance_score, reverse=True)

        return applicable_precedents[:5]  # Return top 5 most relevant

    async def _calculate_fact_similarity(
        self, case_facts: List[str], precedent_facts: List[str]
    ) -> float:
        """Calculate similarity between case facts and precedent facts"""
        if not case_facts or not precedent_facts:
            return 0.0

        # Use AI to calculate semantic similarity
        prompt = f"""
        Compare these two sets of legal facts and rate their similarity from 0.0 to 1.0:

        Current Case Facts:
        {chr(10).join([f"- {fact}" for fact in case_facts])}

        Precedent Case Facts:
        {chr(10).join([f"- {fact}" for fact in precedent_facts])}

        Consider:
        - Similar circumstances
        - Comparable legal issues
        - Analogous fact patterns
        - Relevant distinctions

        Return only a decimal number between 0.0 and 1.0.
        """

        try:
            response = await self.ai_service.generate_response(prompt)
            similarity = float(response.strip())
            return max(0.0, min(1.0, similarity))
        except Exception as e:
            logger.warning(f"⚠️ Fact similarity calculation failed: {e}")
            return 0.5  # Default moderate similarity

    async def _analyze_coverage_clauses(
        self,
        case_facts: List[str],
        liability_type: LiabilityType,
        policy_documents: Optional[List[Dict[str, Any]]]
    ) -> Dict[str, Any]:
        """Analyze applicable coverage clauses"""
        coverage_analysis = {
            "applicable_clauses": [],
            "exclusions": [],
            "coverage_determination": "unknown",
            "policy_limits": {},
            "deductibles": {}
        }

        # Find applicable coverage clauses
        for clause in self.coverage_clauses:
            if self._is_clause_applicable(clause, case_facts, liability_type):
                coverage_analysis["applicable_clauses"].append({
                    "clause_id": clause.clause_id,
                    "title": clause.title,
                    "interpretation": clause.interpretation,
                    "relevance": "high" if clause.clause_type == liability_type.value else "medium"
                })

                if clause.clause_type == "exclusion":
                    coverage_analysis["exclusions"].append(clause.title)

        # Analyze policy documents if provided
        if policy_documents:
            policy_analysis = await self._analyze_policy_documents(
                policy_documents, case_facts, liability_type
            )
            coverage_analysis.update(policy_analysis)

        # Determine overall coverage
        coverage_analysis["coverage_determination"] = await self._determine_coverage(
            coverage_analysis["applicable_clauses"],
            coverage_analysis["exclusions"]
        )

        return coverage_analysis

    def _is_clause_applicable(
        self, clause: CoverageClause, case_facts: List[str], liability_type: LiabilityType
    ) -> bool:
        """Determine if a coverage clause is applicable to the case"""
        # Check if clause type matches liability type
        if clause.clause_type == liability_type.value:
            return True

        # Check if any case facts match applicable scenarios
        case_text = " ".join(case_facts).lower()
        for scenario in clause.applicable_scenarios:
            if scenario.lower() in case_text:
                return True

        return False

    async def _analyze_policy_documents(
        self,
        policy_documents: List[Dict[str, Any]],
        case_facts: List[str],
        liability_type: LiabilityType
    ) -> Dict[str, Any]:
        """Analyze specific policy documents"""
        policy_analysis = {
            "coverage_determination": "covered",
            "policy_limits": {},
            "deductibles": {},
            "specific_exclusions": []
        }

        for doc in policy_documents:
            text = doc.get("text", "")

            # Extract policy limits
            limits = self._extract_policy_limits(text)
            if limits:
                policy_analysis["policy_limits"].update(limits)

            # Extract deductibles
            deductibles = self._extract_deductibles(text)
            if deductibles:
                policy_analysis["deductibles"].update(deductibles)

            # Check for specific exclusions
            exclusions = await self._extract_exclusions(text, case_facts)
            policy_analysis["specific_exclusions"].extend(exclusions)

        return policy_analysis

    def _extract_policy_limits(self, policy_text: str) -> Dict[str, str]:
        """Extract policy limits from policy text"""
        limits = {}

        # Common limit patterns
        limit_patterns = [
            r"per occurrence limit[:\s]+\$?([\d,]+)",
            r"aggregate limit[:\s]+\$?([\d,]+)",
            r"bodily injury[:\s]+\$?([\d,]+)",
            r"property damage[:\s]+\$?([\d,]+)"
        ]

        for pattern in limit_patterns:
            matches = re.findall(pattern, policy_text.lower())
            if matches:
                limit_type = pattern.split('[')[0].replace('\\', '').strip()
                limits[limit_type] = f"${matches[0]}"

        return limits

    def _extract_deductibles(self, policy_text: str) -> Dict[str, str]:
        """Extract deductibles from policy text"""
        deductibles = {}

        # Common deductible patterns
        deductible_patterns = [
            r"deductible[:\s]+\$?([\d,]+)",
            r"self insured retention[:\s]+\$?([\d,]+)"
        ]

        for pattern in deductible_patterns:
            matches = re.findall(pattern, policy_text.lower())
            if matches:
                deductible_type = pattern.split('[')[0].replace('\\', '').strip()
                deductibles[deductible_type] = f"${matches[0]}"

        return deductibles

    async def _extract_exclusions(self, policy_text: str, case_facts: List[str]) -> List[str]:
        """Extract relevant exclusions from policy text"""
        exclusions = []

        # Use AI to identify relevant exclusions
        prompt = f"""
        Analyze this insurance policy text and identify any exclusions that might apply to this case:

        Case Facts:
        {chr(10).join([f"- {fact}" for fact in case_facts])}

        Policy Text (excerpt):
        {policy_text[:2000]}

        Return a list of applicable exclusions, if any.
        """

        try:
            response = await self.ai_service.generate_response(prompt)
            # Parse response for exclusions
            if "no exclusions" not in response.lower():
                exclusions = [line.strip() for line in response.split('\n') if line.strip()]
        except Exception as e:
            logger.warning(f"⚠️ Exclusion extraction failed: {e}")

        return exclusions

    async def _determine_coverage(
        self, applicable_clauses: List[Dict], exclusions: List[str]
    ) -> str:
        """Determine overall coverage status"""
        if not applicable_clauses:
            return "not_covered"

        if exclusions:
            return "coverage_disputed"

        # Check for coverage clauses
        coverage_clauses = [c for c in applicable_clauses if "coverage" in c["title"].lower()]
        if coverage_clauses:
            return "covered"

        return "coverage_uncertain"

    async def _generate_legal_reasoning(
        self,
        case_facts: List[str],
        applicable_precedents: List[LegalPrecedent],
        coverage_analysis: Dict[str, Any]
    ) -> str:
        """Generate comprehensive legal reasoning"""
        reasoning_parts = []

        # Precedent analysis
        if applicable_precedents:
            reasoning_parts.append("PRECEDENT ANALYSIS:")
            for precedent in applicable_precedents[:3]:  # Top 3 precedents
                fault_desc = ", ".join([f"{k}: {v}%" for k, v in precedent.fault_allocation.items()])
                reasoning_parts.append(
                    f"- {precedent.case_name} ({precedent.year}): {precedent.legal_principle}. "
                    f"Fault allocation: {fault_desc}. Relevance: {precedent.relevance_score:.2f}"
                )

        # Coverage analysis
        reasoning_parts.append("\nCOVERAGE ANALYSIS:")
        coverage_status = coverage_analysis.get("coverage_determination", "unknown")
        reasoning_parts.append(f"- Coverage Status: {coverage_status.replace('_', ' ').title()}")

        if coverage_analysis.get("applicable_clauses"):
            reasoning_parts.append("- Applicable Coverage Clauses:")
            for clause in coverage_analysis["applicable_clauses"][:3]:
                reasoning_parts.append(f"  • {clause['title']}: {clause['interpretation']}")

        if coverage_analysis.get("exclusions"):
            reasoning_parts.append("- Potential Exclusions:")
            for exclusion in coverage_analysis["exclusions"][:3]:
                reasoning_parts.append(f"  • {exclusion}")

        # Legal principles
        reasoning_parts.append("\nAPPLICABLE LEGAL PRINCIPLES:")
        reasoning_parts.append("- Occupier's liability requires reasonable care for known hazards")
        reasoning_parts.append("- Contributory negligence may reduce damages based on plaintiff's actions")
        reasoning_parts.append("- Foreseeability of harm is key to establishing liability")

        return "\n".join(reasoning_parts)

    async def _calculate_legal_confidence(
        self, applicable_precedents: List[LegalPrecedent], coverage_analysis: Dict[str, Any]
    ) -> float:
        """Calculate confidence score for legal analysis"""
        confidence_factors = []

        # Precedent strength
        if applicable_precedents:
            avg_relevance = sum(p.relevance_score for p in applicable_precedents) / len(applicable_precedents)
            confidence_factors.append(avg_relevance * 0.4)  # 40% weight
        else:
            confidence_factors.append(0.2)  # Low confidence without precedents

        # Coverage clarity
        coverage_status = coverage_analysis.get("coverage_determination", "unknown")
        coverage_confidence = {
            "covered": 0.9,
            "not_covered": 0.8,
            "coverage_disputed": 0.5,
            "coverage_uncertain": 0.3,
            "unknown": 0.2
        }
        confidence_factors.append(coverage_confidence.get(coverage_status, 0.2) * 0.3)  # 30% weight

        # Documentation quality
        doc_quality = 0.8 if coverage_analysis.get("applicable_clauses") else 0.4
        confidence_factors.append(doc_quality * 0.3)  # 30% weight

        return round(sum(confidence_factors), 2)

    async def _generate_legal_recommendations(
        self,
        applicable_precedents: List[LegalPrecedent],
        coverage_analysis: Dict[str, Any],
        confidence_score: float
    ) -> List[str]:
        """Generate legal recommendations"""
        recommendations = []

        # Precedent-based recommendations
        if applicable_precedents:
            top_precedent = applicable_precedents[0]
            if top_precedent.relevance_score > 0.8:
                recommendations.append(
                    f"Strong precedent support from {top_precedent.case_name}. "
                    f"Consider similar fault allocation: {top_precedent.fault_allocation}"
                )

        # Coverage recommendations
        coverage_status = coverage_analysis.get("coverage_determination", "unknown")
        if coverage_status == "covered":
            recommendations.append("Coverage appears clear under applicable policy clauses")
        elif coverage_status == "coverage_disputed":
            recommendations.append("Coverage may be disputed due to potential exclusions - review carefully")
        elif coverage_status == "not_covered":
            recommendations.append("Coverage appears excluded - consider denial or reservation of rights")

        # Confidence-based recommendations
        if confidence_score < 0.6:
            recommendations.append("Low confidence in analysis - consider legal counsel consultation")
            recommendations.append("Request additional documentation to strengthen analysis")
        elif confidence_score > 0.8:
            recommendations.append("High confidence in analysis - proceed with recommended fault allocation")

        # General recommendations
        recommendations.append("Document all reasoning for audit trail and potential litigation")
        recommendations.append("Consider settlement negotiations based on fault allocation analysis")

        return recommendations

    async def get_precedent_by_id(self, case_id: str) -> Optional[LegalPrecedent]:
        """Retrieve specific precedent by case ID"""
        for precedent in self.precedent_database:
            if precedent.case_id == case_id:
                return precedent
        return None

    async def search_precedents_by_keyword(self, keyword: str) -> List[LegalPrecedent]:
        """Search precedents by keyword"""
        matching_precedents = []
        keyword_lower = keyword.lower()

        for precedent in self.precedent_database:
            # Search in case name, facts, and legal principle
            searchable_text = (
                precedent.case_name.lower() + " " +
                " ".join(precedent.key_facts).lower() + " " +
                precedent.legal_principle.lower()
            )

            if keyword_lower in searchable_text:
                matching_precedents.append(precedent)

        return matching_precedents
