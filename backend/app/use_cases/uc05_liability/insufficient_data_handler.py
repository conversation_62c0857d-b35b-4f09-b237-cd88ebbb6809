"""
🎯 UC05: Insufficient Data Handler
Production-quality system for detecting missing documents and generating professional communications
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import json
from datetime import datetime, timedelta

from app.services.ai_service import AIService
from app.core.config import settings

logger = logging.getLogger(__name__)

class DocumentRequirement(Enum):
    """Required document types for liability claims"""
    FNOL = "first_notice_of_loss"
    INCIDENT_REPORT = "incident_report"
    MEDICAL_REPORT = "medical_report"
    POLICE_REPORT = "police_report"
    WITNESS_STATEMENT = "witness_statement"
    PHOTOS = "photo_evidence"
    INSURANCE_CERTIFICATE = "insurance_certificate"
    CORRESPONDENCE = "email_communication"
    FINANCIAL_DOCUMENTS = "invoices_receipts"

class DataSufficiencyLevel(Enum):
    """Data sufficiency levels"""
    COMPLETE = "complete"
    SUFFICIENT = "sufficient"
    INSUFFICIENT = "insufficient"
    CRITICAL_MISSING = "critical_missing"

@dataclass
class MissingDocument:
    """Missing document information"""
    document_type: DocumentRequirement
    importance: str  # "critical", "important", "optional"
    reason: str
    suggested_source: str
    deadline_days: int

@dataclass
class EmailDraft:
    """Generated email draft"""
    subject: str
    body: str
    recipient_type: str  # "claimant", "adjuster", "legal"
    urgency: str  # "high", "medium", "low"
    follow_up_days: int

@dataclass
class DataSufficiencyAssessment:
    """Complete data sufficiency assessment"""
    level: DataSufficiencyLevel
    confidence_score: float
    missing_documents: List[MissingDocument]
    can_proceed: bool
    recommendation: str
    email_drafts: List[EmailDraft]
    next_steps: List[str]

class InsufficientDataHandler:
    """Advanced insufficient data detection and handling system"""
    
    def __init__(self):
        self.ai_service = AIService()
        self.document_requirements = self._load_document_requirements()
        self.email_templates = self._load_email_templates()
    
    async def assess_data_sufficiency(
        self,
        documents: List[Dict[str, Any]],
        claim_context: Optional[Dict[str, Any]] = None
    ) -> DataSufficiencyAssessment:
        """
        Assess if provided documents are sufficient for liability analysis
        
        Args:
            documents: List of processed documents
            claim_context: Additional context about the claim
            
        Returns:
            DataSufficiencyAssessment with missing documents and recommendations
        """
        try:
            logger.info(f"🔍 Assessing data sufficiency for {len(documents)} documents")
            
            # Step 1: Analyze document coverage
            document_coverage = await self._analyze_document_coverage(documents)
            
            # Step 2: Identify missing critical documents
            missing_docs = await self._identify_missing_documents(
                document_coverage, claim_context
            )
            
            # Step 3: Calculate sufficiency level
            sufficiency_level = await self._calculate_sufficiency_level(
                document_coverage, missing_docs
            )
            
            # Step 4: Determine if we can proceed
            can_proceed = await self._can_proceed_with_analysis(
                sufficiency_level, missing_docs
            )
            
            # Step 5: Generate email drafts if needed
            email_drafts = []
            if not can_proceed:
                email_drafts = await self._generate_email_drafts(missing_docs, claim_context)
            
            # Step 6: Create recommendations
            recommendation = await self._generate_recommendation(
                sufficiency_level, missing_docs, can_proceed
            )
            
            # Step 7: Define next steps
            next_steps = await self._define_next_steps(
                sufficiency_level, missing_docs, can_proceed
            )
            
            assessment = DataSufficiencyAssessment(
                level=sufficiency_level,
                confidence_score=self._calculate_confidence_score(document_coverage),
                missing_documents=missing_docs,
                can_proceed=can_proceed,
                recommendation=recommendation,
                email_drafts=email_drafts,
                next_steps=next_steps
            )
            
            logger.info(f"✅ Data sufficiency assessment complete: {sufficiency_level.value}")
            return assessment
            
        except Exception as e:
            logger.error(f"❌ Data sufficiency assessment failed: {e}")
            raise
    
    async def _analyze_document_coverage(
        self, documents: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Analyze what document types are present"""
        coverage = {req.value: False for req in DocumentRequirement}
        document_details = {}
        
        for doc in documents:
            # Use AI to classify document type
            doc_type = await self._classify_document_type(doc)
            if doc_type in coverage:
                coverage[doc_type] = True
                document_details[doc_type] = {
                    "filename": doc.get("filename", ""),
                    "quality": await self._assess_document_quality(doc),
                    "completeness": await self._assess_document_completeness(doc)
                }
        
        return {
            "coverage": coverage,
            "details": document_details,
            "total_documents": len(documents)
        }
    
    async def _classify_document_type(self, document: Dict[str, Any]) -> str:
        """Classify document type using AI"""
        text = document.get("text", "")[:1000]  # First 1000 chars
        filename = document.get("filename", "")
        
        prompt = f"""
        Classify this insurance document into one of these specific types:
        - first_notice_of_loss: FNOL forms, initial claim reports
        - incident_report: Detailed incident descriptions, accident reports
        - medical_report: Medical records, doctor reports, hospital records
        - police_report: Police incident reports, traffic citations
        - witness_statement: Witness accounts, statements
        - photo_evidence: Photos, images, visual evidence
        - insurance_certificate: Insurance policies, certificates, coverage docs
        - email_communication: Emails, letters, correspondence
        - invoices_receipts: Medical bills, repair invoices, financial documents
        
        Filename: {filename}
        Content sample: {text}
        
        Return only the classification type (e.g., "medical_report").
        """
        
        try:
            response = await self.ai_service.generate_response(prompt)
            return response.strip().lower()
        except Exception as e:
            logger.warning(f"⚠️ Document classification failed: {e}")
            return "unknown"
    
    def _load_document_requirements(self) -> Dict[str, Any]:
        """Load document requirements configuration"""
        return {
            "critical": [
                DocumentRequirement.FNOL,
                DocumentRequirement.INCIDENT_REPORT
            ],
            "important": [
                DocumentRequirement.MEDICAL_REPORT,
                DocumentRequirement.PHOTOS,
                DocumentRequirement.INSURANCE_CERTIFICATE
            ],
            "optional": [
                DocumentRequirement.POLICE_REPORT,
                DocumentRequirement.WITNESS_STATEMENT,
                DocumentRequirement.CORRESPONDENCE,
                DocumentRequirement.FINANCIAL_DOCUMENTS
            ]
        }
    
    def _load_email_templates(self) -> Dict[str, str]:
        """Load email templates for different scenarios"""
        return {
            "missing_medical": """
Subject: Additional Medical Documentation Required - Claim #{claim_number}

Dear {claimant_name},

Thank you for submitting your insurance claim. To proceed with the assessment of your claim, we require additional medical documentation to properly evaluate your injuries and associated costs.

Please provide:
- Medical reports from treating physicians
- Hospital records (if applicable)
- Diagnostic test results (X-rays, MRI, etc.)
- Medical invoices and receipts

You can submit these documents through our secure portal or by email. Please ensure all documents are legible and complete.

We aim to process your claim as quickly as possible and appreciate your cooperation in providing these essential documents.

Best regards,
Claims Processing Team
Zurich Insurance
""",
            "missing_incident": """
Subject: Incident Report Required - Claim #{claim_number}

Dear {claimant_name},

We are processing your insurance claim and require a detailed incident report to complete our assessment.

Please provide:
- Detailed description of how the incident occurred
- Date, time, and location of the incident
- Names and contact information of any witnesses
- Photos of the incident scene (if available)

This information is crucial for determining liability and processing your claim accurately.

Please submit this information within 10 business days to avoid delays in processing.

Best regards,
Claims Processing Team
Zurich Insurance
""",
            "missing_photos": """
Subject: Photo Evidence Requested - Claim #{claim_number}

Dear {claimant_name},

To complete the assessment of your claim, we require photographic evidence of the incident and any damages.

Please provide:
- Photos of the incident location
- Images showing any property damage
- Pictures of any injuries (if applicable and comfortable)
- Photos of any relevant conditions that contributed to the incident

High-quality, clear images will help us process your claim more efficiently.

Best regards,
Claims Processing Team
Zurich Insurance
"""
        }
    
    def _calculate_confidence_score(self, document_coverage: Dict[str, Any]) -> float:
        """Calculate confidence score based on document coverage"""
        coverage = document_coverage["coverage"]
        total_docs = len(coverage)
        present_docs = sum(1 for present in coverage.values() if present)
        
        # Weight critical documents more heavily
        critical_present = sum(1 for req in self.document_requirements["critical"] 
                             if coverage.get(req.value, False))
        critical_total = len(self.document_requirements["critical"])
        
        # Calculate weighted score
        basic_score = present_docs / total_docs if total_docs > 0 else 0
        critical_score = critical_present / critical_total if critical_total > 0 else 0
        
        # Weighted average (70% critical, 30% overall)
        confidence = (0.7 * critical_score) + (0.3 * basic_score)
        return round(confidence, 2)

    async def _assess_document_quality(self, document: Dict[str, Any]) -> float:
        """Assess the quality of a document"""
        text = document.get("text", "")

        # Basic quality indicators
        quality_score = 0.5  # Base score

        # Text length indicator
        if len(text) > 100:
            quality_score += 0.2
        if len(text) > 500:
            quality_score += 0.2

        # OCR confidence if available
        if "confidence" in document:
            ocr_confidence = document["confidence"]
            quality_score = (quality_score + ocr_confidence) / 2

        return min(quality_score, 1.0)

    async def _assess_document_completeness(self, document: Dict[str, Any]) -> float:
        """Assess if document appears complete"""
        text = document.get("text", "")

        # Look for completion indicators
        completion_indicators = [
            "signature", "signed", "date:", "total:", "amount:",
            "conclusion", "recommendation", "diagnosis"
        ]

        found_indicators = sum(1 for indicator in completion_indicators
                             if indicator.lower() in text.lower())

        completeness = min(found_indicators / 3, 1.0)  # Normalize to 0-1
        return completeness

    async def _identify_missing_documents(
        self,
        document_coverage: Dict[str, Any],
        claim_context: Optional[Dict[str, Any]]
    ) -> List[MissingDocument]:
        """Identify missing critical documents"""
        missing_docs = []
        coverage = document_coverage["coverage"]

        # Check critical documents
        for req in self.document_requirements["critical"]:
            if not coverage.get(req.value, False):
                missing_docs.append(MissingDocument(
                    document_type=req,
                    importance="critical",
                    reason=f"Required for liability assessment",
                    suggested_source="claimant",
                    deadline_days=5
                ))

        # Check important documents based on claim type
        claim_type = claim_context.get("claim_type", "") if claim_context else ""

        if "injury" in claim_type.lower() or "medical" in claim_type.lower():
            if not coverage.get(DocumentRequirement.MEDICAL_REPORT.value, False):
                missing_docs.append(MissingDocument(
                    document_type=DocumentRequirement.MEDICAL_REPORT,
                    importance="critical",
                    reason="Medical documentation required for injury claims",
                    suggested_source="medical_provider",
                    deadline_days=10
                ))

        if "property" in claim_type.lower() or "damage" in claim_type.lower():
            if not coverage.get(DocumentRequirement.PHOTOS.value, False):
                missing_docs.append(MissingDocument(
                    document_type=DocumentRequirement.PHOTOS,
                    importance="important",
                    reason="Visual evidence required for property damage assessment",
                    suggested_source="claimant",
                    deadline_days=7
                ))

        return missing_docs

    async def _calculate_sufficiency_level(
        self,
        document_coverage: Dict[str, Any],
        missing_docs: List[MissingDocument]
    ) -> DataSufficiencyLevel:
        """Calculate overall data sufficiency level"""
        critical_missing = [doc for doc in missing_docs if doc.importance == "critical"]
        important_missing = [doc for doc in missing_docs if doc.importance == "important"]

        if len(critical_missing) > 1:
            return DataSufficiencyLevel.CRITICAL_MISSING
        elif len(critical_missing) == 1:
            return DataSufficiencyLevel.INSUFFICIENT
        elif len(important_missing) > 2:
            return DataSufficiencyLevel.INSUFFICIENT
        elif len(important_missing) > 0:
            return DataSufficiencyLevel.SUFFICIENT
        else:
            return DataSufficiencyLevel.COMPLETE

    async def _can_proceed_with_analysis(
        self,
        sufficiency_level: DataSufficiencyLevel,
        missing_docs: List[MissingDocument]
    ) -> bool:
        """Determine if analysis can proceed with current data"""
        if sufficiency_level == DataSufficiencyLevel.CRITICAL_MISSING:
            return False
        elif sufficiency_level == DataSufficiencyLevel.INSUFFICIENT:
            # Check if we have minimum viable documents
            critical_missing = [doc for doc in missing_docs if doc.importance == "critical"]
            return len(critical_missing) == 0
        else:
            return True

    async def _generate_email_drafts(
        self,
        missing_docs: List[MissingDocument],
        claim_context: Optional[Dict[str, Any]]
    ) -> List[EmailDraft]:
        """Generate professional email drafts for requesting missing documents"""
        email_drafts = []

        # Group missing documents by type for efficient communication
        doc_groups = {}
        for doc in missing_docs:
            doc_type = doc.document_type.value
            if doc_type not in doc_groups:
                doc_groups[doc_type] = []
            doc_groups[doc_type].append(doc)

        # Generate emails for each document group
        for doc_type, docs in doc_groups.items():
            template_key = self._get_template_key(doc_type)
            if template_key in self.email_templates:
                email_draft = await self._create_email_from_template(
                    template_key, docs, claim_context
                )
                email_drafts.append(email_draft)

        return email_drafts

    def _get_template_key(self, doc_type: str) -> str:
        """Map document type to email template key"""
        mapping = {
            "medical_report": "missing_medical",
            "incident_report": "missing_incident",
            "photo_evidence": "missing_photos",
            "first_notice_of_loss": "missing_incident",
            "police_report": "missing_incident"
        }
        return mapping.get(doc_type, "missing_incident")

    async def _create_email_from_template(
        self,
        template_key: str,
        missing_docs: List[MissingDocument],
        claim_context: Optional[Dict[str, Any]]
    ) -> EmailDraft:
        """Create email draft from template"""
        template = self.email_templates[template_key]

        # Extract claim information
        claim_number = claim_context.get("claim_number", "PENDING") if claim_context else "PENDING"
        claimant_name = claim_context.get("claimant_name", "Valued Customer") if claim_context else "Valued Customer"

        # Replace placeholders
        email_body = template.replace("{claim_number}", claim_number)
        email_body = email_body.replace("{claimant_name}", claimant_name)

        # Determine urgency based on document importance
        urgency = "high" if any(doc.importance == "critical" for doc in missing_docs) else "medium"

        # Extract subject from template
        subject_line = template.split('\n')[1].replace("Subject: ", "")
        subject_line = subject_line.replace("{claim_number}", claim_number)

        return EmailDraft(
            subject=subject_line,
            body=email_body,
            recipient_type="claimant",
            urgency=urgency,
            follow_up_days=min(doc.deadline_days for doc in missing_docs)
        )

    async def _generate_recommendation(
        self,
        sufficiency_level: DataSufficiencyLevel,
        missing_docs: List[MissingDocument],
        can_proceed: bool
    ) -> str:
        """Generate recommendation based on data sufficiency"""
        if sufficiency_level == DataSufficiencyLevel.COMPLETE:
            return "All required documents are present. Proceed with full liability analysis."
        elif sufficiency_level == DataSufficiencyLevel.SUFFICIENT:
            return "Sufficient documentation available. Proceed with analysis while requesting additional supporting documents."
        elif sufficiency_level == DataSufficiencyLevel.INSUFFICIENT:
            if can_proceed:
                return "Minimum viable documentation present. Proceed with preliminary analysis and request missing documents."
            else:
                return "Insufficient documentation. Request critical missing documents before proceeding."
        else:  # CRITICAL_MISSING
            return "Critical documents missing. Analysis cannot proceed until essential documentation is provided."

    async def _define_next_steps(
        self,
        sufficiency_level: DataSufficiencyLevel,
        missing_docs: List[MissingDocument],
        can_proceed: bool
    ) -> List[str]:
        """Define next steps based on assessment"""
        next_steps = []

        if can_proceed:
            next_steps.append("Proceed with liability analysis using available documents")
            if missing_docs:
                next_steps.append("Send document requests to claimant")
                next_steps.append("Set follow-up reminders for missing documents")
        else:
            next_steps.append("Send urgent document requests to claimant")
            next_steps.append("Pause claim processing until critical documents received")
            next_steps.append("Set escalation timeline for non-response")

        # Add specific steps based on missing document types
        for doc in missing_docs:
            if doc.document_type == DocumentRequirement.MEDICAL_REPORT:
                next_steps.append("Contact medical providers if claimant consent available")
            elif doc.document_type == DocumentRequirement.POLICE_REPORT:
                next_steps.append("Attempt to obtain police report independently")

        return next_steps

    async def generate_follow_up_communication(
        self,
        assessment: DataSufficiencyAssessment,
        days_elapsed: int
    ) -> Optional[EmailDraft]:
        """Generate follow-up communication for overdue documents"""
        if not assessment.missing_documents:
            return None

        # Check if follow-up is needed
        urgent_docs = [doc for doc in assessment.missing_documents
                      if days_elapsed >= doc.deadline_days]

        if not urgent_docs:
            return None

        # Generate escalated follow-up email
        subject = f"URGENT: Outstanding Documents Required - Claim Processing Delayed"

        body = f"""
Dear Valued Customer,

We previously requested additional documentation for your insurance claim, but have not yet received the required materials.

Outstanding Documents:
{chr(10).join([f"- {doc.document_type.value.replace('_', ' ').title()}" for doc in urgent_docs])}

To avoid further delays in processing your claim, please provide these documents within 3 business days.

If you are experiencing difficulties obtaining these documents, please contact us immediately so we can assist you.

Failure to provide required documentation may result in claim denial or significant processing delays.

Best regards,
Claims Processing Team
Zurich Insurance
"""

        return EmailDraft(
            subject=subject,
            body=body,
            recipient_type="claimant",
            urgency="high",
            follow_up_days=3
        )

    async def update_assessment_with_new_documents(
        self,
        original_assessment: DataSufficiencyAssessment,
        new_documents: List[Dict[str, Any]]
    ) -> DataSufficiencyAssessment:
        """Update assessment when new documents are received"""
        # Combine all documents for re-assessment
        all_documents = new_documents  # In practice, would combine with original docs

        # Re-run assessment
        updated_assessment = await self.assess_data_sufficiency(all_documents)

        logger.info(f"📄 Assessment updated: {original_assessment.level.value} → {updated_assessment.level.value}")

        return updated_assessment
