"""
🎯 UC05: Professional Communication Generator
Advanced email and communication generation system for liability claims
"""

import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import json
from datetime import datetime, timedelta

from app.services.ai_service import AIService
from app.core.config import settings

logger = logging.getLogger(__name__)

class CommunicationType(Enum):
    """Types of communications"""
    DOCUMENT_REQUEST = "document_request"
    STATUS_UPDATE = "status_update"
    DECISION_NOTIFICATION = "decision_notification"
    SETTLEMENT_OFFER = "settlement_offer"
    DENIAL_LETTER = "denial_letter"
    FOLLOW_UP = "follow_up"
    ESCALATION = "escalation"

class RecipientType(Enum):
    """Types of recipients"""
    CLAIMANT = "claimant"
    ADJUSTER = "adjuster"
    LEGAL_COUNSEL = "legal_counsel"
    SENIOR_MANAGEMENT = "senior_management"
    MEDICAL_PROVIDER = "medical_provider"
    THIRD_PARTY = "third_party"

@dataclass
class CommunicationTemplate:
    """Communication template structure"""
    template_id: str
    communication_type: CommunicationType
    recipient_type: RecipientType
    subject_template: str
    body_template: str
    urgency_level: str
    follow_up_days: int
    required_variables: List[str]

@dataclass
class GeneratedCommunication:
    """Generated communication result"""
    communication_id: str
    communication_type: CommunicationType
    recipient_type: RecipientType
    subject: str
    body: str
    urgency: str
    follow_up_date: datetime
    variables_used: Dict[str, str]
    template_id: str

class ProfessionalCommunicationGenerator:
    """Advanced communication generation system"""
    
    def __init__(self):
        self.ai_service = AIService()
        self.templates = self._load_communication_templates()
        self.style_guide = self._load_style_guide()
    
    async def generate_communication(
        self,
        communication_type: CommunicationType,
        recipient_type: RecipientType,
        context_data: Dict[str, Any],
        custom_instructions: Optional[str] = None
    ) -> GeneratedCommunication:
        """
        Generate professional communication
        
        Args:
            communication_type: Type of communication to generate
            recipient_type: Target recipient type
            context_data: Context data for personalization
            custom_instructions: Additional custom instructions
            
        Returns:
            GeneratedCommunication with subject, body, and metadata
        """
        try:
            logger.info(f"📧 Generating {communication_type.value} for {recipient_type.value}")
            
            # Step 1: Select appropriate template
            template = self._select_template(communication_type, recipient_type)
            
            # Step 2: Prepare variables
            variables = await self._prepare_variables(context_data, template)
            
            # Step 3: Generate subject
            subject = await self._generate_subject(template, variables, custom_instructions)
            
            # Step 4: Generate body
            body = await self._generate_body(template, variables, custom_instructions)
            
            # Step 5: Apply style and tone
            subject, body = await self._apply_style_and_tone(
                subject, body, recipient_type, communication_type
            )
            
            # Step 6: Calculate follow-up date
            follow_up_date = datetime.now() + timedelta(days=template.follow_up_days)
            
            communication = GeneratedCommunication(
                communication_id=f"comm_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                communication_type=communication_type,
                recipient_type=recipient_type,
                subject=subject,
                body=body,
                urgency=template.urgency_level,
                follow_up_date=follow_up_date,
                variables_used=variables,
                template_id=template.template_id
            )
            
            logger.info(f"✅ Communication generated: {communication.communication_id}")
            return communication
            
        except Exception as e:
            logger.error(f"❌ Communication generation failed: {e}")
            raise
    
    def _select_template(
        self, communication_type: CommunicationType, recipient_type: RecipientType
    ) -> CommunicationTemplate:
        """Select appropriate template"""
        
        # Find exact match first
        for template in self.templates:
            if (template.communication_type == communication_type and 
                template.recipient_type == recipient_type):
                return template
        
        # Find fallback template
        for template in self.templates:
            if template.communication_type == communication_type:
                return template
        
        # Default template
        return self.templates[0]
    
    async def _prepare_variables(
        self, context_data: Dict[str, Any], template: CommunicationTemplate
    ) -> Dict[str, str]:
        """Prepare variables for template substitution"""
        
        variables = {}
        
        # Standard variables
        variables["claim_number"] = context_data.get("claim_number", "PENDING")
        variables["claimant_name"] = context_data.get("claimant_name", "Valued Customer")
        variables["incident_date"] = context_data.get("incident_date", "Unknown")
        variables["current_date"] = datetime.now().strftime("%B %d, %Y")
        variables["company_name"] = "Zurich Insurance"
        
        # Liability-specific variables
        if "fault_percentage" in context_data:
            variables["fault_percentage"] = str(context_data["fault_percentage"])
        
        if "settlement_amount" in context_data:
            variables["settlement_amount"] = f"${context_data['settlement_amount']:,.2f}"
        
        if "missing_documents" in context_data:
            missing_docs = context_data["missing_documents"]
            if isinstance(missing_docs, list):
                variables["missing_documents_list"] = "\n".join([f"• {doc}" for doc in missing_docs])
            else:
                variables["missing_documents_list"] = str(missing_docs)
        
        # Legal analysis variables
        if "legal_precedents" in context_data:
            precedents = context_data["legal_precedents"]
            if isinstance(precedents, list) and precedents:
                variables["legal_precedent"] = precedents[0].get("case_name", "")
        
        return variables
    
    async def _generate_subject(
        self, template: CommunicationTemplate, variables: Dict[str, str], custom_instructions: Optional[str]
    ) -> str:
        """Generate email subject line"""
        
        # Start with template
        subject = template.subject_template
        
        # Replace variables
        for var_name, var_value in variables.items():
            placeholder = "{" + var_name + "}"
            subject = subject.replace(placeholder, var_value)
        
        # Use AI to enhance if custom instructions provided
        if custom_instructions:
            prompt = f"""
            Enhance this email subject line based on the instructions:
            
            Current subject: {subject}
            Instructions: {custom_instructions}
            
            Make it professional, clear, and appropriate for insurance communications.
            Return only the enhanced subject line.
            """
            
            try:
                enhanced_subject = await self.ai_service.generate_response(prompt)
                return enhanced_subject.strip()
            except Exception as e:
                logger.warning(f"⚠️ Subject enhancement failed: {e}")
        
        return subject
    
    async def _generate_body(
        self, template: CommunicationTemplate, variables: Dict[str, str], custom_instructions: Optional[str]
    ) -> str:
        """Generate email body"""
        
        # Start with template
        body = template.body_template
        
        # Replace variables
        for var_name, var_value in variables.items():
            placeholder = "{" + var_name + "}"
            body = body.replace(placeholder, var_value)
        
        # Use AI to enhance if custom instructions provided
        if custom_instructions:
            prompt = f"""
            Enhance this email body based on the instructions:
            
            Current body: {body}
            Instructions: {custom_instructions}
            
            Maintain professional insurance communication tone.
            Keep the structure but improve clarity and completeness.
            """
            
            try:
                enhanced_body = await self.ai_service.generate_response(prompt)
                return enhanced_body.strip()
            except Exception as e:
                logger.warning(f"⚠️ Body enhancement failed: {e}")
        
        return body
    
    async def _apply_style_and_tone(
        self, subject: str, body: str, recipient_type: RecipientType, communication_type: CommunicationType
    ) -> tuple[str, str]:
        """Apply appropriate style and tone"""
        
        # Get style guidelines
        style_rules = self.style_guide.get(recipient_type.value, {})
        
        # Apply tone adjustments
        if recipient_type == RecipientType.CLAIMANT:
            # Empathetic and clear for claimants
            tone = "empathetic and clear"
        elif recipient_type == RecipientType.LEGAL_COUNSEL:
            # Formal and precise for legal
            tone = "formal and precise"
        elif recipient_type == RecipientType.SENIOR_MANAGEMENT:
            # Concise and executive for management
            tone = "concise and executive"
        else:
            # Professional standard
            tone = "professional"
        
        # Use AI to apply tone if needed
        if communication_type in [CommunicationType.DENIAL_LETTER, CommunicationType.SETTLEMENT_OFFER]:
            prompt = f"""
            Review and adjust this insurance communication for appropriate tone:
            
            Subject: {subject}
            Body: {body}
            
            Target tone: {tone}
            Recipient: {recipient_type.value}
            Type: {communication_type.value}
            
            Ensure the communication is professional, legally appropriate, and maintains the right tone.
            Return the adjusted subject and body separated by "---BODY---".
            """
            
            try:
                response = await self.ai_service.generate_response(prompt)
                if "---BODY---" in response:
                    parts = response.split("---BODY---")
                    subject = parts[0].strip()
                    body = parts[1].strip()
            except Exception as e:
                logger.warning(f"⚠️ Style application failed: {e}")
        
        return subject, body

    def _load_communication_templates(self) -> List[CommunicationTemplate]:
        """Load communication templates"""
        return [
            # Document Request Templates
            CommunicationTemplate(
                template_id="doc_request_claimant",
                communication_type=CommunicationType.DOCUMENT_REQUEST,
                recipient_type=RecipientType.CLAIMANT,
                subject_template="Additional Documentation Required - Claim #{claim_number}",
                body_template="""Dear {claimant_name},

Thank you for submitting your insurance claim #{claim_number} regarding the incident on {incident_date}.

To complete our assessment, we require the following additional documentation:

{missing_documents_list}

Please provide these documents within 10 business days to avoid delays in processing your claim. You may submit documents through our secure portal or by email.

If you have any questions or need assistance obtaining these documents, please don't hesitate to contact us.

Best regards,
Claims Processing Team
{company_name}""",
                urgency_level="medium",
                follow_up_days=10,
                required_variables=["claim_number", "claimant_name", "missing_documents_list"]
            ),

            # Status Update Templates
            CommunicationTemplate(
                template_id="status_update_claimant",
                communication_type=CommunicationType.STATUS_UPDATE,
                recipient_type=RecipientType.CLAIMANT,
                subject_template="Claim Status Update - #{claim_number}",
                body_template="""Dear {claimant_name},

We are writing to provide you with an update on your insurance claim #{claim_number}.

Current Status: Under Review
Progress: We have completed our initial assessment and are currently reviewing the liability determination.

Next Steps: We expect to complete our review within the next 5-7 business days and will contact you with our decision.

If you have any questions about your claim, please feel free to contact us.

Best regards,
Claims Processing Team
{company_name}""",
                urgency_level="low",
                follow_up_days=7,
                required_variables=["claim_number", "claimant_name"]
            ),

            # Decision Notification Templates
            CommunicationTemplate(
                template_id="decision_notification_claimant",
                communication_type=CommunicationType.DECISION_NOTIFICATION,
                recipient_type=RecipientType.CLAIMANT,
                subject_template="Claim Decision - #{claim_number}",
                body_template="""Dear {claimant_name},

We have completed our investigation of your insurance claim #{claim_number} regarding the incident on {incident_date}.

Decision: Claim Approved
Liability Assessment: Based on our investigation, we have determined the fault allocation for this incident.

Our analysis considered all available evidence, including witness statements, incident reports, and applicable legal precedents.

We will be contacting you separately regarding the settlement details.

If you have any questions about this decision, please contact us.

Best regards,
Claims Processing Team
{company_name}""",
                urgency_level="high",
                follow_up_days=3,
                required_variables=["claim_number", "claimant_name", "incident_date"]
            ),

            # Settlement Offer Templates
            CommunicationTemplate(
                template_id="settlement_offer_claimant",
                communication_type=CommunicationType.SETTLEMENT_OFFER,
                recipient_type=RecipientType.CLAIMANT,
                subject_template="Settlement Offer - Claim #{claim_number}",
                body_template="""Dear {claimant_name},

Following our investigation of claim #{claim_number}, we are pleased to offer a settlement in the amount of {settlement_amount}.

This settlement offer is based on:
- Our liability assessment
- Documented damages and expenses
- Applicable legal precedents

This offer remains open for 30 days from the date of this letter. Please review the enclosed settlement agreement and contact us if you wish to accept this offer or if you have any questions.

Best regards,
Claims Processing Team
{company_name}""",
                urgency_level="high",
                follow_up_days=30,
                required_variables=["claim_number", "claimant_name", "settlement_amount"]
            ),

            # Denial Letter Templates
            CommunicationTemplate(
                template_id="denial_letter_claimant",
                communication_type=CommunicationType.DENIAL_LETTER,
                recipient_type=RecipientType.CLAIMANT,
                subject_template="Claim Decision - #{claim_number}",
                body_template="""Dear {claimant_name},

We have completed our investigation of your insurance claim #{claim_number} regarding the incident on {incident_date}.

Decision: Claim Denied

After careful review of all available evidence and documentation, we have determined that coverage is not available under the applicable insurance policy for the following reason(s):

[Specific denial reasons will be detailed here based on the case]

This decision is based on our thorough investigation and analysis of the facts and circumstances surrounding your claim.

If you disagree with this decision, you have the right to request a review. Please contact us within 30 days if you wish to appeal this decision.

Best regards,
Claims Processing Team
{company_name}""",
                urgency_level="high",
                follow_up_days=30,
                required_variables=["claim_number", "claimant_name", "incident_date"]
            ),

            # Legal Counsel Communication
            CommunicationTemplate(
                template_id="legal_review_request",
                communication_type=CommunicationType.ESCALATION,
                recipient_type=RecipientType.LEGAL_COUNSEL,
                subject_template="Legal Review Required - Claim #{claim_number}",
                body_template="""Legal Team,

Claim #{claim_number} requires legal review due to complex liability issues.

Case Summary:
- Claimant: {claimant_name}
- Incident Date: {incident_date}
- Liability Assessment: Complex determination required

Key Issues:
- Coverage interpretation questions
- Precedent analysis needed
- Potential litigation risk

Please review and provide guidance on liability determination and settlement strategy.

Claims Adjuster
{company_name}""",
                urgency_level="high",
                follow_up_days=5,
                required_variables=["claim_number", "claimant_name", "incident_date"]
            )
        ]

    def _load_style_guide(self) -> Dict[str, Dict[str, str]]:
        """Load style guide for different recipient types"""
        return {
            "claimant": {
                "tone": "empathetic and professional",
                "language": "clear and accessible",
                "structure": "organized with clear sections"
            },
            "legal_counsel": {
                "tone": "formal and precise",
                "language": "legal terminology appropriate",
                "structure": "detailed and comprehensive"
            },
            "adjuster": {
                "tone": "professional and direct",
                "language": "industry terminology",
                "structure": "concise and action-oriented"
            },
            "senior_management": {
                "tone": "executive and strategic",
                "language": "business-focused",
                "structure": "summary with key points"
            }
        }

    async def generate_batch_communications(
        self, communication_requests: List[Dict[str, Any]]
    ) -> List[GeneratedCommunication]:
        """Generate multiple communications in batch"""

        communications = []

        for request in communication_requests:
            try:
                comm = await self.generate_communication(
                    CommunicationType(request["type"]),
                    RecipientType(request["recipient"]),
                    request["context"],
                    request.get("custom_instructions")
                )
                communications.append(comm)
            except Exception as e:
                logger.error(f"❌ Batch communication failed: {e}")
                continue

        return communications

    async def generate_follow_up_communication(
        self, original_communication: GeneratedCommunication, days_elapsed: int
    ) -> Optional[GeneratedCommunication]:
        """Generate follow-up communication"""

        if days_elapsed < original_communication.follow_up_days:
            return None

        # Generate follow-up based on original type
        follow_up_context = {
            "claim_number": original_communication.variables_used.get("claim_number", ""),
            "claimant_name": original_communication.variables_used.get("claimant_name", ""),
            "original_date": original_communication.follow_up_date.strftime("%B %d, %Y")
        }

        return await self.generate_communication(
            CommunicationType.FOLLOW_UP,
            original_communication.recipient_type,
            follow_up_context,
            f"Follow-up to {original_communication.communication_type.value}"
        )
