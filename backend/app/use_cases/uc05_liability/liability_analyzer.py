"""
🎯 UC05: Liability Decisions Analyzer
Advanced AI-powered liability assessment for insurance claims
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import re
import json

from app.services.ai_service import AIService
from app.core.config import settings
from app.use_cases.uc05_liability.insufficient_data_handler import (
    InsufficientDataHandler, DataSufficiencyAssessment
)
from app.use_cases.uc05_liability.legal_precedent_engine import (
    LegalPrecedentEngine, LegalAnalysis, LiabilityType, Province
)
from app.use_cases.uc05_liability.workflow_decision_engine import (
    WorkflowDecisionEngine, WorkflowStage, WorkflowState
)
from app.use_cases.uc05_liability.enhanced_document_processor import (
    EnhancedDocumentProcessor, DocumentAnalysis
)
from app.use_cases.uc05_liability.loss_quantum_calculator import (
    LossQuantumCalculator, LossQuantumResult
)

logger = logging.getLogger(__name__)

class DocumentType(Enum):
    """Document types for liability analysis"""
    FNOL = "first_notice_of_loss"
    EMAIL = "email_communication"
    CERTIFICATE = "insurance_certificate"
    MEDICAL_REPORT = "medical_report"
    INCIDENT_REPORT = "incident_report"
    WITNESS_STATEMENT = "witness_statement"
    POLICE_REPORT = "police_report"
    PHOTO = "photo_evidence"
    OTHER = "other"

@dataclass
class Evidence:
    """Evidence extracted from documents"""
    type: str
    content: str
    confidence: float
    source_document: str
    relevance_score: float

@dataclass
class LiabilityAssessment:
    """Enhanced liability assessment result"""
    insured_fault_percentage: float
    third_party_fault_percentage: float
    confidence_score: float
    supporting_evidence: List[Evidence]
    risk_factors: Dict[str, Any]
    recommendation: Dict[str, Any]
    reasoning: str
    # New enhanced components
    data_sufficiency: Optional[Any] = None  # DataSufficiencyAssessment
    legal_analysis: Optional[Any] = None    # LegalAnalysis
    document_analyses: List[Any] = None     # List[DocumentAnalysis]
    loss_quantum: Optional[Any] = None      # LossQuantumResult
    workflow_state: Optional[Any] = None    # WorkflowState
    email_drafts: List[Dict[str, Any]] = None
    next_steps: List[str] = None

class LiabilityAnalyzer:
    """Production-quality liability analyzer for UC05 with complete workflow"""

    def __init__(self):
        self.ai_service = AIService()
        self.evidence_patterns = self._load_evidence_patterns()
        self.legal_precedents = self._load_legal_precedents()

        # Initialize new production components
        self.data_handler = InsufficientDataHandler()
        self.legal_engine = LegalPrecedentEngine()
        self.workflow_engine = WorkflowDecisionEngine()
        self.document_processor = EnhancedDocumentProcessor()
        self.quantum_calculator = LossQuantumCalculator()
    
    async def analyze_liability(
        self,
        documents: List[Dict[str, Any]],
        claim_context: Optional[Dict[str, Any]] = None
    ) -> LiabilityAssessment:
        """
        Production-quality liability analysis with complete workflow

        Args:
            documents: List of processed documents with OCR text
            claim_context: Additional context about the claim

        Returns:
            Enhanced LiabilityAssessment with complete analysis and recommendations
        """
        try:
            logger.info(f"🔍 Starting production liability analysis for {len(documents)} documents")

            # PHASE 1: Data Sufficiency Assessment
            logger.info("📋 Phase 1: Assessing data sufficiency...")
            data_sufficiency = await self.data_handler.assess_data_sufficiency(documents, claim_context)

            # PHASE 2: Enhanced Document Processing
            logger.info("📄 Phase 2: Enhanced document processing...")
            document_analyses = []
            for i, doc in enumerate(documents):
                analysis = await self.document_processor.process_document(doc, f"doc_{i}")
                document_analyses.append(analysis)

            # PHASE 3: Workflow Decision Making
            logger.info("🔄 Phase 3: Workflow decision processing...")
            workflow_decision, workflow_state = await self.workflow_engine.process_workflow_stage(
                WorkflowStage.DATA_SUFFICIENCY_CHECK, documents, claim_context or {}
            )

            # Check if we can proceed with analysis
            if not data_sufficiency.can_proceed:
                logger.warning("⚠️ Insufficient data - generating communication drafts")
                return LiabilityAssessment(
                    insured_fault_percentage=0.0,
                    third_party_fault_percentage=0.0,
                    confidence_score=0.0,
                    supporting_evidence=[],
                    risk_factors={"data_sufficiency": "insufficient"},
                    recommendation={"action": "request_documents", "priority": "high"},
                    reasoning="Analysis cannot proceed due to insufficient documentation",
                    data_sufficiency=data_sufficiency,
                    legal_analysis=None,
                    document_analyses=document_analyses,
                    loss_quantum=None,
                    workflow_state=workflow_state,
                    email_drafts=[draft.__dict__ for draft in data_sufficiency.email_drafts],
                    next_steps=data_sufficiency.next_steps
                )

            # PHASE 4: Legal Precedent Analysis
            logger.info("⚖️ Phase 4: Legal precedent analysis...")
            case_facts = await self._extract_case_facts_from_analyses(document_analyses)
            liability_type = self._determine_liability_type(claim_context or {})
            province = self._determine_province(claim_context or {})

            legal_analysis = await self.legal_engine.analyze_legal_precedents(
                case_facts, liability_type, province, documents
            )

            # PHASE 5: Traditional Evidence Extraction and Analysis
            logger.info("🔍 Phase 5: Evidence extraction and fault analysis...")
            evidence_list = await self._extract_evidence(documents)
            incident_analysis = await self._analyze_incident(evidence_list, claim_context)
            fault_assessment = await self._assess_fault(incident_analysis, evidence_list)

            # PHASE 6: Loss Quantum Calculation
            logger.info("💰 Phase 6: Loss quantum calculation...")
            financial_docs = [doc for doc in documents if self._is_financial_document(doc)]
            loss_quantum = None

            if financial_docs and fault_assessment["insured_fault"] > 0:
                fault_allocation = {
                    "insured": fault_assessment["insured_fault"],
                    "third_party": fault_assessment["third_party_fault"]
                }
                loss_quantum = await self.quantum_calculator.calculate_loss_quantum(
                    financial_docs, fault_allocation, claim_context
                )

            # PHASE 7: Enhanced Confidence and Recommendations
            logger.info("📊 Phase 7: Final assessment compilation...")
            confidence = await self._calculate_enhanced_confidence(
                evidence_list, fault_assessment, data_sufficiency, legal_analysis
            )

            recommendation = await self._generate_enhanced_recommendation(
                fault_assessment, confidence, data_sufficiency, legal_analysis, loss_quantum
            )

            # PHASE 8: Compile Enhanced Assessment
            assessment = LiabilityAssessment(
                insured_fault_percentage=fault_assessment["insured_fault"],
                third_party_fault_percentage=fault_assessment["third_party_fault"],
                confidence_score=confidence,
                supporting_evidence=evidence_list,
                risk_factors=fault_assessment["risk_factors"],
                recommendation=recommendation,
                reasoning=fault_assessment["reasoning"],
                data_sufficiency=data_sufficiency,
                legal_analysis=legal_analysis,
                document_analyses=document_analyses,
                loss_quantum=loss_quantum,
                workflow_state=workflow_state,
                email_drafts=[draft.__dict__ for draft in data_sufficiency.email_drafts] if data_sufficiency.email_drafts else [],
                next_steps=data_sufficiency.next_steps or []
            )

            logger.info(f"✅ Production liability analysis complete: {assessment.insured_fault_percentage}% insured fault")
            logger.info(f"📈 Confidence: {confidence:.2f}, Data sufficiency: {data_sufficiency.level.value}")

            return assessment

        except Exception as e:
            logger.error(f"❌ Production liability analysis failed: {e}")
            raise

    async def _extract_case_facts_from_analyses(self, document_analyses: List[Any]) -> List[str]:
        """Extract case facts from document analyses"""
        case_facts = []

        for analysis in document_analyses:
            # Extract key facts from evidence
            for evidence in analysis.extracted_evidence:
                if evidence.evidence_type in ["incident_factor", "witness_observation", "incident_detail"]:
                    case_facts.append(evidence.content)

            # Add summary as a fact
            if analysis.summary and len(analysis.summary) > 20:
                case_facts.append(analysis.summary[:200])  # Truncate long summaries

        return case_facts[:10]  # Return top 10 most relevant facts

    def _determine_liability_type(self, claim_context: Dict[str, Any]) -> Any:  # LiabilityType
        """Determine liability type from claim context"""
        claim_type = claim_context.get("claim_type", "").lower()

        if "premises" in claim_type or "slip" in claim_type or "fall" in claim_type:
            return LiabilityType.PREMISES_LIABILITY
        elif "motor" in claim_type or "vehicle" in claim_type:
            return LiabilityType.MOTOR_VEHICLE
        elif "product" in claim_type:
            return LiabilityType.PRODUCT_LIABILITY
        else:
            return LiabilityType.GENERAL_LIABILITY

    def _determine_province(self, claim_context: Dict[str, Any]) -> Any:  # Province
        """Determine province from claim context"""
        location = claim_context.get("location", "").upper()

        if "ONTARIO" in location or "ON" in location or "TORONTO" in location:
            return Province.ON
        elif "BRITISH COLUMBIA" in location or "BC" in location or "VANCOUVER" in location:
            return Province.BC
        elif "ALBERTA" in location or "AB" in location or "CALGARY" in location:
            return Province.AB
        elif "QUEBEC" in location or "QC" in location or "MONTREAL" in location:
            return Province.QC
        else:
            return Province.ON  # Default to Ontario

    def _is_financial_document(self, document: Dict[str, Any]) -> bool:
        """Check if document is financial in nature"""
        text = document.get("text", "").lower()
        filename = document.get("filename", "").lower()

        financial_keywords = [
            "invoice", "receipt", "bill", "payment", "amount", "total",
            "cost", "expense", "charge", "$", "medical bill"
        ]

        return any(keyword in text or keyword in filename for keyword in financial_keywords)

    async def _calculate_enhanced_confidence(
        self,
        evidence_list: List[Evidence],
        fault_assessment: Dict[str, Any],
        data_sufficiency: Any,  # DataSufficiencyAssessment
        legal_analysis: Any     # LegalAnalysis
    ) -> float:
        """Calculate enhanced confidence score"""

        # Base confidence from traditional analysis
        base_confidence = await self._calculate_confidence(evidence_list, fault_assessment)

        # Data sufficiency factor
        data_factor = data_sufficiency.confidence_score

        # Legal analysis factor
        legal_factor = legal_analysis.confidence_score if legal_analysis else 0.5

        # Weighted average
        enhanced_confidence = (
            0.4 * base_confidence +
            0.3 * data_factor +
            0.3 * legal_factor
        )

        return round(enhanced_confidence, 2)

    async def _generate_enhanced_recommendation(
        self,
        fault_assessment: Dict[str, Any],
        confidence: float,
        data_sufficiency: Any,  # DataSufficiencyAssessment
        legal_analysis: Any,    # LegalAnalysis
        loss_quantum: Any       # LossQuantumResult
    ) -> Dict[str, Any]:
        """Generate enhanced recommendations"""

        # Base recommendation
        base_recommendation = await self._generate_recommendation(fault_assessment, confidence)

        # Enhanced recommendations
        enhanced_rec = {
            **base_recommendation,
            "data_sufficiency_level": data_sufficiency.level.value,
            "legal_precedent_support": len(legal_analysis.applicable_precedents) if legal_analysis else 0,
            "settlement_amount": float(loss_quantum.settlement_recommendation) if loss_quantum else None,
            "next_actions": []
        }

        # Add specific next actions
        if data_sufficiency.level.value in ["insufficient", "critical_missing"]:
            enhanced_rec["next_actions"].append("request_missing_documents")

        if legal_analysis and legal_analysis.confidence_score < 0.6:
            enhanced_rec["next_actions"].append("legal_review_required")

        if loss_quantum and loss_quantum.settlement_recommendation > 50000:
            enhanced_rec["next_actions"].append("senior_approval_required")

        if confidence < 0.7:
            enhanced_rec["next_actions"].append("manual_review_recommended")

        return enhanced_rec
    
    async def _extract_evidence(self, documents: List[Dict[str, Any]]) -> List[Evidence]:
        """Extract relevant evidence from documents"""
        evidence_list = []
        
        for doc in documents:
            try:
                # Classify document type
                doc_type = await self._classify_document(doc)
                
                # Extract evidence based on document type
                if doc_type == DocumentType.FNOL:
                    evidence = await self._extract_fnol_evidence(doc)
                elif doc_type == DocumentType.EMAIL:
                    evidence = await self._extract_email_evidence(doc)
                elif doc_type == DocumentType.CERTIFICATE:
                    evidence = await self._extract_certificate_evidence(doc)
                elif doc_type == DocumentType.MEDICAL_REPORT:
                    evidence = await self._extract_medical_evidence(doc)
                elif doc_type == DocumentType.INCIDENT_REPORT:
                    evidence = await self._extract_incident_evidence(doc)
                else:
                    evidence = await self._extract_general_evidence(doc)
                
                evidence_list.extend(evidence)
                
            except Exception as e:
                logger.warning(f"⚠️ Failed to extract evidence from document: {e}")
                continue
        
        return evidence_list
    
    async def _classify_document(self, document: Dict[str, Any]) -> DocumentType:
        """Classify document type using AI"""
        text = document.get("text", "")
        filename = document.get("filename", "")
        
        # Use AI to classify document
        prompt = f"""
        Classify this insurance document into one of these types:
        - FNOL (First Notice of Loss)
        - EMAIL (Email communication)
        - CERTIFICATE (Insurance certificate)
        - MEDICAL_REPORT (Medical report/records)
        - INCIDENT_REPORT (Incident report)
        - WITNESS_STATEMENT (Witness statement)
        - POLICE_REPORT (Police report)
        - PHOTO (Photo evidence)
        - OTHER (Other document)
        
        Filename: {filename}
        Text sample: {text[:500]}...
        
        Return only the classification type.
        """
        
        try:
            classification = await self.ai_service.generate_response(prompt)
            classification = classification.strip().upper()
            
            # Map to enum
            for doc_type in DocumentType:
                if doc_type.name in classification:
                    return doc_type
            
            return DocumentType.OTHER
            
        except Exception as e:
            logger.warning(f"⚠️ Document classification failed: {e}")
            return DocumentType.OTHER
    
    async def _extract_fnol_evidence(self, document: Dict[str, Any]) -> List[Evidence]:
        """Extract evidence from First Notice of Loss"""
        text = document.get("text", "")
        
        prompt = f"""
        Extract key liability evidence from this First Notice of Loss document:
        
        {text}
        
        Focus on:
        1. Incident location and time
        2. Description of what happened
        3. Parties involved
        4. Injuries or damages
        5. Fault indicators
        6. Weather/environmental conditions
        
        Return as JSON array with format:
        [{{
            "type": "incident_location",
            "content": "extracted information",
            "relevance_score": 0.8
        }}]
        """
        
        try:
            response = await self.ai_service.generate_response(prompt)
            evidence_data = json.loads(response)
            
            evidence_list = []
            for item in evidence_data:
                evidence = Evidence(
                    type=item["type"],
                    content=item["content"],
                    confidence=0.85,  # FNOL typically reliable
                    source_document=document.get("filename", "FNOL"),
                    relevance_score=item.get("relevance_score", 0.7)
                )
                evidence_list.append(evidence)
            
            return evidence_list
            
        except Exception as e:
            logger.warning(f"⚠️ FNOL evidence extraction failed: {e}")
            return []
    
    async def _extract_email_evidence(self, document: Dict[str, Any]) -> List[Evidence]:
        """Extract evidence from email communications"""
        text = document.get("text", "")
        
        # Look for key patterns in emails
        evidence_list = []
        
        # Settlement discussions
        if re.search(r'settlement|settle|pay|payment|compensation', text, re.IGNORECASE):
            evidence_list.append(Evidence(
                type="settlement_discussion",
                content="Email contains settlement discussions",
                confidence=0.9,
                source_document=document.get("filename", "Email"),
                relevance_score=0.8
            ))
        
        # Admission of fault
        fault_patterns = [
            r'my fault', r'i was wrong', r'i caused', r'i am responsible',
            r'sorry for', r'apologize for', r'my mistake'
        ]
        
        for pattern in fault_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                evidence_list.append(Evidence(
                    type="fault_admission",
                    content=f"Potential fault admission found: {pattern}",
                    confidence=0.7,
                    source_document=document.get("filename", "Email"),
                    relevance_score=0.9
                ))
                break
        
        return evidence_list
    
    async def _extract_certificate_evidence(self, document: Dict[str, Any]) -> List[Evidence]:
        """Extract evidence from insurance certificates"""
        text = document.get("text", "")
        evidence_list = []
        
        # Extract coverage limits
        coverage_pattern = r'\$[\d,]+(?:\.\d{2})?'
        coverage_matches = re.findall(coverage_pattern, text)
        
        if coverage_matches:
            evidence_list.append(Evidence(
                type="coverage_limits",
                content=f"Coverage limits found: {', '.join(coverage_matches)}",
                confidence=0.95,
                source_document=document.get("filename", "Certificate"),
                relevance_score=0.6
            ))
        
        # Extract policy period
        date_pattern = r'\d{1,2}[-/]\d{1,2}[-/]\d{4}'
        dates = re.findall(date_pattern, text)
        
        if len(dates) >= 2:
            evidence_list.append(Evidence(
                type="policy_period",
                content=f"Policy period: {dates[0]} to {dates[1]}",
                confidence=0.9,
                source_document=document.get("filename", "Certificate"),
                relevance_score=0.5
            ))
        
        return evidence_list
    
    async def _extract_medical_evidence(self, document: Dict[str, Any]) -> List[Evidence]:
        """Extract evidence from medical reports"""
        text = document.get("text", "")
        evidence_list = []
        
        # Look for injury descriptions
        injury_keywords = [
            'fracture', 'broken', 'sprain', 'strain', 'contusion',
            'laceration', 'concussion', 'whiplash', 'herniation'
        ]
        
        for keyword in injury_keywords:
            if keyword in text.lower():
                evidence_list.append(Evidence(
                    type="injury_type",
                    content=f"Injury type: {keyword}",
                    confidence=0.8,
                    source_document=document.get("filename", "Medical"),
                    relevance_score=0.7
                ))
        
        return evidence_list
    
    async def _extract_incident_evidence(self, document: Dict[str, Any]) -> List[Evidence]:
        """Extract evidence from incident reports"""
        text = document.get("text", "")
        
        prompt = f"""
        Extract liability-relevant information from this incident report:
        
        {text}
        
        Focus on:
        1. Sequence of events
        2. Contributing factors
        3. Safety violations
        4. Environmental conditions
        5. Witness observations
        
        Return as JSON array.
        """
        
        try:
            response = await self.ai_service.generate_response(prompt)
            evidence_data = json.loads(response)
            
            evidence_list = []
            for item in evidence_data:
                evidence = Evidence(
                    type=item["type"],
                    content=item["content"],
                    confidence=0.8,
                    source_document=document.get("filename", "Incident Report"),
                    relevance_score=item.get("relevance_score", 0.7)
                )
                evidence_list.append(evidence)
            
            return evidence_list
            
        except Exception as e:
            logger.warning(f"⚠️ Incident evidence extraction failed: {e}")
            return []
    
    async def _extract_general_evidence(self, document: Dict[str, Any]) -> List[Evidence]:
        """Extract general evidence from any document"""
        text = document.get("text", "")
        evidence_list = []
        
        # Look for key liability indicators
        liability_keywords = {
            'negligence': 0.8,
            'breach of duty': 0.9,
            'reasonable care': 0.7,
            'proximate cause': 0.8,
            'contributory negligence': 0.9
        }
        
        for keyword, relevance in liability_keywords.items():
            if keyword in text.lower():
                evidence_list.append(Evidence(
                    type="legal_concept",
                    content=f"Legal concept mentioned: {keyword}",
                    confidence=0.6,
                    source_document=document.get("filename", "Document"),
                    relevance_score=relevance
                ))
        
        return evidence_list
    
    def _load_evidence_patterns(self) -> Dict[str, Any]:
        """Load evidence extraction patterns"""
        return {
            "fault_indicators": [
                "speeding", "distracted", "intoxicated", "reckless",
                "violation", "failed to", "negligent", "careless"
            ],
            "injury_severity": [
                "minor", "moderate", "severe", "critical", "fatal"
            ],
            "environmental_factors": [
                "weather", "visibility", "road conditions", "lighting"
            ]
        }
    
    def _load_legal_precedents(self) -> Dict[str, Any]:
        """Load Canadian legal precedents for liability"""
        return {
            "comparative_negligence": {
                "description": "Canadian law follows comparative negligence",
                "application": "Fault can be split between parties"
            },
            "duty_of_care": {
                "description": "Standard duty of care requirements",
                "application": "Must prove breach of reasonable care"
            }
        }
    
    async def _analyze_incident(
        self, 
        evidence_list: List[Evidence], 
        claim_context: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Analyze the incident based on evidence"""
        
        # Compile evidence for analysis
        evidence_summary = "\n".join([
            f"- {ev.type}: {ev.content} (confidence: {ev.confidence})"
            for ev in evidence_list
        ])
        
        prompt = f"""
        Analyze this insurance liability incident based on the evidence:
        
        Evidence:
        {evidence_summary}
        
        Additional Context: {claim_context or 'None provided'}
        
        Provide analysis covering:
        1. Sequence of events
        2. Contributing factors
        3. Applicable legal standards
        4. Comparative fault considerations
        
        Return as structured JSON.
        """
        
        try:
            response = await self.ai_service.generate_response(prompt)
            return json.loads(response)
        except Exception as e:
            logger.warning(f"⚠️ Incident analysis failed: {e}")
            return {"error": str(e)}
    
    async def _assess_fault(
        self, 
        incident_analysis: Dict[str, Any], 
        evidence_list: List[Evidence]
    ) -> Dict[str, Any]:
        """Assess fault allocation between parties"""
        
        # High-relevance evidence gets more weight
        weighted_evidence = [
            ev for ev in evidence_list 
            if ev.relevance_score > 0.7
        ]
        
        prompt = f"""
        Based on this incident analysis and evidence, determine fault allocation:
        
        Incident Analysis: {json.dumps(incident_analysis, indent=2)}
        
        High-Priority Evidence:
        {chr(10).join([f"- {ev.type}: {ev.content}" for ev in weighted_evidence])}
        
        Apply Canadian comparative negligence law to determine:
        1. Insured party fault percentage (0-100%)
        2. Third party fault percentage (0-100%)
        3. Key reasoning for allocation
        4. Risk factors that could affect the assessment
        
        Return as JSON:
        {{
            "insured_fault": 65,
            "third_party_fault": 35,
            "reasoning": "detailed explanation",
            "risk_factors": {{
                "coverage_adequacy": "sufficient/insufficient",
                "legal_complexity": "low/medium/high",
                "evidence_quality": "strong/moderate/weak"
            }}
        }}
        """
        
        try:
            response = await self.ai_service.generate_response(prompt)
            fault_data = json.loads(response)
            
            # Ensure percentages add up to 100
            total = fault_data["insured_fault"] + fault_data["third_party_fault"]
            if total != 100:
                # Normalize
                fault_data["insured_fault"] = round(fault_data["insured_fault"] * 100 / total)
                fault_data["third_party_fault"] = 100 - fault_data["insured_fault"]
            
            return fault_data
            
        except Exception as e:
            logger.warning(f"⚠️ Fault assessment failed: {e}")
            return {
                "insured_fault": 50,
                "third_party_fault": 50,
                "reasoning": "Unable to determine fault allocation due to insufficient evidence",
                "risk_factors": {
                    "coverage_adequacy": "unknown",
                    "legal_complexity": "high",
                    "evidence_quality": "weak"
                }
            }
    
    async def _calculate_confidence(
        self, 
        evidence_list: List[Evidence], 
        fault_assessment: Dict[str, Any]
    ) -> float:
        """Calculate confidence score for the assessment"""
        
        # Factors affecting confidence
        evidence_quality = sum(ev.confidence * ev.relevance_score for ev in evidence_list) / len(evidence_list) if evidence_list else 0
        evidence_quantity = min(len(evidence_list) / 10, 1.0)  # Normalize to 1.0
        
        # Risk factors impact
        risk_factors = fault_assessment.get("risk_factors", {})
        evidence_strength = 1.0 if risk_factors.get("evidence_quality") == "strong" else 0.7
        legal_clarity = 1.0 if risk_factors.get("legal_complexity") == "low" else 0.8
        
        # Calculate overall confidence
        confidence = (evidence_quality * 0.4 + 
                     evidence_quantity * 0.2 + 
                     evidence_strength * 0.2 + 
                     legal_clarity * 0.2)
        
        return round(confidence, 2)
    
    async def _generate_recommendation(
        self, 
        fault_assessment: Dict[str, Any], 
        confidence: float
    ) -> Dict[str, Any]:
        """Generate recommendations based on assessment"""
        
        insured_fault = fault_assessment["insured_fault"]
        
        if confidence < 0.6:
            action = "investigate_further"
            reasoning = "Low confidence requires additional investigation"
        elif insured_fault <= 25:
            action = "deny_claim"
            reasoning = "Minimal insured fault suggests claim denial"
        elif insured_fault <= 75:
            action = "negotiate_settlement"
            reasoning = "Shared fault suggests settlement negotiation"
        else:
            action = "accept_liability"
            reasoning = "High insured fault suggests accepting liability"
        
        return {
            "action": action,
            "reasoning": reasoning,
            "next_steps": [
                "Review coverage limits",
                "Assess potential exposure",
                "Consider legal consultation" if confidence < 0.7 else "Proceed with recommendation"
            ],
            "estimated_exposure": "TBD - requires coverage analysis"
        }
