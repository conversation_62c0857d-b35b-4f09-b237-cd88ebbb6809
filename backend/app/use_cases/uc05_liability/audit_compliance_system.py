"""
🎯 UC05: Audit Trail & Compliance System
Comprehensive logging and regulatory compliance tracking for liability decisions
"""

import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
import json
import uuid
from datetime import datetime, timezone
import hashlib

from app.core.config import settings

logger = logging.getLogger(__name__)

class AuditEventType(Enum):
    """Types of audit events"""
    DOCUMENT_UPLOADED = "document_uploaded"
    DOCUMENT_PROCESSED = "document_processed"
    DATA_SUFFICIENCY_ASSESSED = "data_sufficiency_assessed"
    LEGAL_ANALYSIS_PERFORMED = "legal_analysis_performed"
    LIABILITY_CALCULATED = "liability_calculated"
    LOSS_QUANTUM_CALCULATED = "loss_quantum_calculated"
    COMMUNICATION_GENERATED = "communication_generated"
    DECISION_MADE = "decision_made"
    WORKFLOW_STAGE_COMPLETED = "workflow_stage_completed"
    MANUAL_OVERRIDE = "manual_override"
    SYSTEM_ERROR = "system_error"

class ComplianceStandard(Enum):
    """Compliance standards"""
    CANADIAN_INSURANCE_LAW = "canadian_insurance_law"
    PRIVACY_ACT = "privacy_act"
    PIPEDA = "pipeda"  # Personal Information Protection and Electronic Documents Act
    PROVINCIAL_REGULATIONS = "provincial_regulations"
    FAIR_PRACTICES = "fair_practices"

@dataclass
class AuditEvent:
    """Individual audit event"""
    event_id: str
    event_type: AuditEventType
    timestamp: datetime
    user_id: Optional[str]
    claim_id: str
    event_data: Dict[str, Any]
    system_version: str
    ip_address: Optional[str]
    session_id: Optional[str]
    data_hash: str

@dataclass
class ComplianceCheck:
    """Compliance check result"""
    standard: ComplianceStandard
    compliant: bool
    details: str
    recommendations: List[str]
    severity: str  # "low", "medium", "high", "critical"

@dataclass
class AuditTrail:
    """Complete audit trail for a claim"""
    claim_id: str
    events: List[AuditEvent]
    compliance_checks: List[ComplianceCheck]
    created_at: datetime
    last_updated: datetime
    retention_until: datetime

class AuditComplianceSystem:
    """Comprehensive audit and compliance tracking system"""
    
    def __init__(self):
        self.compliance_rules = self._load_compliance_rules()
        self.retention_policies = self._load_retention_policies()
    
    async def log_audit_event(
        self,
        event_type: AuditEventType,
        claim_id: str,
        event_data: Dict[str, Any],
        user_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> AuditEvent:
        """
        Log an audit event
        
        Args:
            event_type: Type of event being logged
            claim_id: Claim identifier
            event_data: Event-specific data
            user_id: User who triggered the event
            ip_address: IP address of the request
            session_id: Session identifier
            
        Returns:
            AuditEvent with complete audit information
        """
        try:
            # Generate event ID
            event_id = str(uuid.uuid4())
            
            # Create timestamp
            timestamp = datetime.now(timezone.utc)
            
            # Calculate data hash for integrity
            data_hash = self._calculate_data_hash(event_data)
            
            # Create audit event
            audit_event = AuditEvent(
                event_id=event_id,
                event_type=event_type,
                timestamp=timestamp,
                user_id=user_id,
                claim_id=claim_id,
                event_data=event_data,
                system_version=settings.VERSION if hasattr(settings, 'VERSION') else "1.0.0",
                ip_address=ip_address,
                session_id=session_id,
                data_hash=data_hash
            )
            
            # Store audit event (in production, this would go to a secure audit database)
            await self._store_audit_event(audit_event)
            
            # Log for immediate visibility
            logger.info(f"📋 Audit event logged: {event_type.value} for claim {claim_id}")
            
            return audit_event
            
        except Exception as e:
            logger.error(f"❌ Failed to log audit event: {e}")
            raise
    
    async def perform_compliance_check(
        self, claim_id: str, audit_trail: List[AuditEvent]
    ) -> List[ComplianceCheck]:
        """
        Perform comprehensive compliance checks
        
        Args:
            claim_id: Claim identifier
            audit_trail: List of audit events for the claim
            
        Returns:
            List of compliance check results
        """
        try:
            logger.info(f"🔍 Performing compliance checks for claim {claim_id}")
            
            compliance_results = []
            
            # Check Canadian Insurance Law compliance
            canadian_law_check = await self._check_canadian_insurance_law(audit_trail)
            compliance_results.append(canadian_law_check)
            
            # Check Privacy Act compliance
            privacy_check = await self._check_privacy_compliance(audit_trail)
            compliance_results.append(privacy_check)
            
            # Check PIPEDA compliance
            pipeda_check = await self._check_pipeda_compliance(audit_trail)
            compliance_results.append(pipeda_check)
            
            # Check Fair Practices compliance
            fair_practices_check = await self._check_fair_practices(audit_trail)
            compliance_results.append(fair_practices_check)
            
            # Log compliance summary
            compliant_count = sum(1 for check in compliance_results if check.compliant)
            logger.info(f"✅ Compliance check complete: {compliant_count}/{len(compliance_results)} standards met")
            
            return compliance_results
            
        except Exception as e:
            logger.error(f"❌ Compliance check failed: {e}")
            raise
    
    async def generate_audit_report(
        self, claim_id: str, include_sensitive_data: bool = False
    ) -> Dict[str, Any]:
        """
        Generate comprehensive audit report
        
        Args:
            claim_id: Claim identifier
            include_sensitive_data: Whether to include sensitive information
            
        Returns:
            Comprehensive audit report
        """
        try:
            # Retrieve audit trail
            audit_trail = await self._retrieve_audit_trail(claim_id)
            
            # Perform compliance checks
            compliance_checks = await self.perform_compliance_check(claim_id, audit_trail.events)
            
            # Generate report
            report = {
                "claim_id": claim_id,
                "report_generated_at": datetime.now(timezone.utc).isoformat(),
                "total_events": len(audit_trail.events),
                "event_summary": self._summarize_events(audit_trail.events),
                "compliance_summary": self._summarize_compliance(compliance_checks),
                "timeline": self._generate_timeline(audit_trail.events),
                "data_integrity": self._verify_data_integrity(audit_trail.events),
                "retention_status": self._check_retention_status(audit_trail)
            }
            
            # Include detailed events if requested
            if include_sensitive_data:
                report["detailed_events"] = [asdict(event) for event in audit_trail.events]
                report["detailed_compliance"] = [asdict(check) for check in compliance_checks]
            
            logger.info(f"📊 Audit report generated for claim {claim_id}")
            return report
            
        except Exception as e:
            logger.error(f"❌ Audit report generation failed: {e}")
            raise
    
    def _calculate_data_hash(self, data: Dict[str, Any]) -> str:
        """Calculate hash for data integrity"""
        data_str = json.dumps(data, sort_keys=True, default=str)
        return hashlib.sha256(data_str.encode()).hexdigest()
    
    async def _store_audit_event(self, audit_event: AuditEvent):
        """Store audit event securely"""
        # In production, this would store to a secure, immutable audit database
        # For now, we'll log it
        logger.info(f"Storing audit event: {audit_event.event_id}")
    
    async def _retrieve_audit_trail(self, claim_id: str) -> AuditTrail:
        """Retrieve complete audit trail for a claim"""
        # Mock implementation - in production, retrieve from audit database
        return AuditTrail(
            claim_id=claim_id,
            events=[],
            compliance_checks=[],
            created_at=datetime.now(timezone.utc),
            last_updated=datetime.now(timezone.utc),
            retention_until=datetime.now(timezone.utc)
        )
    
    async def _check_canadian_insurance_law(self, audit_trail: List[AuditEvent]) -> ComplianceCheck:
        """Check compliance with Canadian insurance law"""
        
        # Check for required decision documentation
        decision_events = [e for e in audit_trail if e.event_type == AuditEventType.DECISION_MADE]
        liability_events = [e for e in audit_trail if e.event_type == AuditEventType.LIABILITY_CALCULATED]
        
        compliant = len(decision_events) > 0 and len(liability_events) > 0
        
        details = "All liability decisions properly documented and calculated" if compliant else "Missing required decision documentation"
        
        recommendations = []
        if not compliant:
            recommendations.append("Ensure all liability decisions are properly documented")
            recommendations.append("Maintain complete audit trail of decision-making process")
        
        return ComplianceCheck(
            standard=ComplianceStandard.CANADIAN_INSURANCE_LAW,
            compliant=compliant,
            details=details,
            recommendations=recommendations,
            severity="high" if not compliant else "low"
        )
    
    async def _check_privacy_compliance(self, audit_trail: List[AuditEvent]) -> ComplianceCheck:
        """Check privacy law compliance"""
        
        # Check for proper data handling
        document_events = [e for e in audit_trail if e.event_type == AuditEventType.DOCUMENT_PROCESSED]
        
        # Verify no sensitive data is logged inappropriately
        compliant = True
        for event in document_events:
            if self._contains_sensitive_data(event.event_data):
                compliant = False
                break
        
        details = "Personal information handled in compliance with privacy laws" if compliant else "Potential privacy law violations detected"
        
        recommendations = []
        if not compliant:
            recommendations.append("Review data handling procedures")
            recommendations.append("Implement additional privacy safeguards")
        
        return ComplianceCheck(
            standard=ComplianceStandard.PRIVACY_ACT,
            compliant=compliant,
            details=details,
            recommendations=recommendations,
            severity="critical" if not compliant else "low"
        )
    
    async def _check_pipeda_compliance(self, audit_trail: List[AuditEvent]) -> ComplianceCheck:
        """Check PIPEDA compliance"""
        
        # Check for consent and purpose limitation
        compliant = True  # Simplified check
        
        details = "Personal information collection and use complies with PIPEDA"
        recommendations = ["Continue monitoring data collection practices"]
        
        return ComplianceCheck(
            standard=ComplianceStandard.PIPEDA,
            compliant=compliant,
            details=details,
            recommendations=recommendations,
            severity="medium"
        )
    
    async def _check_fair_practices(self, audit_trail: List[AuditEvent]) -> ComplianceCheck:
        """Check fair insurance practices"""
        
        # Check for timely processing and fair treatment
        processing_times = self._calculate_processing_times(audit_trail)
        compliant = all(time <= 30 for time in processing_times)  # 30 day standard
        
        details = "Claim processed within fair timeframes" if compliant else "Processing delays detected"
        
        recommendations = []
        if not compliant:
            recommendations.append("Review processing workflows for efficiency")
            recommendations.append("Implement automated reminders for time-sensitive tasks")
        
        return ComplianceCheck(
            standard=ComplianceStandard.FAIR_PRACTICES,
            compliant=compliant,
            details=details,
            recommendations=recommendations,
            severity="medium" if not compliant else "low"
        )
    
    def _contains_sensitive_data(self, data: Dict[str, Any]) -> bool:
        """Check if data contains sensitive information"""
        sensitive_patterns = ["ssn", "sin", "credit_card", "password", "personal_health"]
        data_str = json.dumps(data).lower()
        return any(pattern in data_str for pattern in sensitive_patterns)
    
    def _calculate_processing_times(self, audit_trail: List[AuditEvent]) -> List[int]:
        """Calculate processing times between key events"""
        # Simplified implementation
        return [5, 10, 15]  # Mock processing times in days
    
    def _summarize_events(self, events: List[AuditEvent]) -> Dict[str, int]:
        """Summarize events by type"""
        summary = {}
        for event in events:
            event_type = event.event_type.value
            summary[event_type] = summary.get(event_type, 0) + 1
        return summary
    
    def _summarize_compliance(self, checks: List[ComplianceCheck]) -> Dict[str, Any]:
        """Summarize compliance check results"""
        total_checks = len(checks)
        compliant_checks = sum(1 for check in checks if check.compliant)
        
        return {
            "total_standards_checked": total_checks,
            "compliant_standards": compliant_checks,
            "compliance_percentage": (compliant_checks / total_checks * 100) if total_checks > 0 else 0,
            "critical_issues": [check.standard.value for check in checks if not check.compliant and check.severity == "critical"]
        }
    
    def _generate_timeline(self, events: List[AuditEvent]) -> List[Dict[str, Any]]:
        """Generate timeline of events"""
        timeline = []
        for event in sorted(events, key=lambda e: e.timestamp):
            timeline.append({
                "timestamp": event.timestamp.isoformat(),
                "event_type": event.event_type.value,
                "description": f"{event.event_type.value.replace('_', ' ').title()}"
            })
        return timeline
    
    def _verify_data_integrity(self, events: List[AuditEvent]) -> Dict[str, Any]:
        """Verify data integrity of audit trail"""
        total_events = len(events)
        verified_events = sum(1 for event in events if event.data_hash)  # Simplified check
        
        return {
            "total_events": total_events,
            "verified_events": verified_events,
            "integrity_percentage": (verified_events / total_events * 100) if total_events > 0 else 0,
            "integrity_status": "verified" if verified_events == total_events else "partial"
        }
    
    def _check_retention_status(self, audit_trail: AuditTrail) -> Dict[str, Any]:
        """Check data retention status"""
        days_until_deletion = (audit_trail.retention_until - datetime.now(timezone.utc)).days
        
        return {
            "retention_until": audit_trail.retention_until.isoformat(),
            "days_remaining": max(0, days_until_deletion),
            "status": "active" if days_until_deletion > 0 else "expired"
        }
    
    def _load_compliance_rules(self) -> Dict[str, Any]:
        """Load compliance rules configuration"""
        return {
            "canadian_insurance_law": {
                "required_documentation": ["liability_assessment", "decision_rationale"],
                "timeframe_requirements": {"initial_response": 5, "final_decision": 30}
            },
            "privacy_act": {
                "data_minimization": True,
                "consent_required": True,
                "retention_limits": {"personal_data": 2555}  # 7 years in days
            }
        }
    
    def _load_retention_policies(self) -> Dict[str, int]:
        """Load data retention policies"""
        return {
            "audit_events": 2555,  # 7 years
            "claim_data": 2555,    # 7 years
            "personal_data": 2555, # 7 years
            "system_logs": 365     # 1 year
        }
