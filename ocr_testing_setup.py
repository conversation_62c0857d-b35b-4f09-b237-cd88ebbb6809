#!/usr/bin/env python3
"""
OCR Engine Testing Suite
Tests individual OCR engines with demo documents from the internet
"""

import os
import time
import requests
import base64
from typing import Dict, List, Any
import json
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OCRTester:
    def __init__(self):
        self.demo_documents = {
            "invoice": "https://github.com/excid3/receipts/raw/main/examples/invoice.pdf",
            "receipt": "https://ocr.space/Content/Images/receipt-ocr-original.jpg",
            "form": "https://ocr.space/Content/Images/form-sample.jpg",
            "handwriting": "https://ocr.space/Content/Images/handwriting-sample.jpg",
            "table": "https://ocr.space/Content/Images/table-sample.jpg",
            "mixed_content": "https://dl.a9t9.com/ocr/mixed-content-sample.jpg"
        }
        
        # Create results directory
        self.results_dir = Path("ocr_test_results")
        self.results_dir.mkdir(exist_ok=True)
        
        # Configuration for different engines
        self.engine_configs = {
            "tesseract": self.test_tesseract,
            "paddleocr": self.test_paddleocr,
            "easyocr": self.test_easyocr,
            "ocr_space": self.test_ocr_space,
            "azure_doc_intelligence": self.test_azure,
            "aws_textract": self.test_textract,
            "google_vision": self.test_google_vision
        }

    def download_demo_file(self, url: str, filename: str) -> str:
        """Download demo file if not exists"""
        local_path = self.results_dir / filename
        
        if not local_path.exists():
            logger.info(f"Downloading {filename} from {url}")
            try:
                response = requests.get(url, timeout=30)
                response.raise_for_status()
                
                with open(local_path, 'wb') as f:
                    f.write(response.content)
                logger.info(f"Downloaded {filename} successfully")
            except Exception as e:
                logger.error(f"Failed to download {filename}: {e}")
                return None
        
        return str(local_path)

    def test_tesseract(self, file_path: str) -> Dict[str, Any]:
        """Test Tesseract OCR"""
        try:
            import pytesseract
            from PIL import Image
            import cv2
            import numpy as np
            
            start_time = time.time()
            
            # Handle different file types
            if file_path.lower().endswith('.pdf'):
                import fitz  # PyMuPDF
                doc = fitz.open(file_path)
                page = doc[0]
                pix = page.get_pixmap()
                img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
            else:
                img = Image.open(file_path)
            
            # Convert to OpenCV format for preprocessing
            img_cv = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
            
            # Preprocessing
            gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
            denoised = cv2.fastNlMeansDenoising(gray)
            
            # OCR with different configurations
            configs = [
                '--oem 3 --psm 6',  # Default
                '--oem 3 --psm 4',  # Single column
                '--oem 3 --psm 3',  # Fully automatic
            ]
            
            results = []
            for config in configs:
                text = pytesseract.image_to_string(denoised, config=config)
                confidence = pytesseract.image_to_data(denoised, config=config, output_type=pytesseract.Output.DICT)
                avg_conf = np.mean([int(conf) for conf in confidence['conf'] if int(conf) > 0])
                
                results.append({
                    'config': config,
                    'text': text.strip(),
                    'confidence': avg_conf
                })
            
            # Return best result
            best_result = max(results, key=lambda x: x['confidence'])
            
            return {
                'engine': 'tesseract',
                'success': True,
                'text': best_result['text'],
                'confidence': best_result['confidence'],
                'processing_time': time.time() - start_time,
                'config_used': best_result['config']
            }
            
        except Exception as e:
            return {
                'engine': 'tesseract',
                'success': False,
                'error': str(e),
                'processing_time': time.time() - start_time if 'start_time' in locals() else 0
            }

    def test_paddleocr(self, file_path: str) -> Dict[str, Any]:
        """Test PaddleOCR"""
        try:
            from paddleocr import PaddleOCR
            
            start_time = time.time()
            
            # Initialize PaddleOCR
            ocr = PaddleOCR(use_angle_cls=True, lang='en', use_gpu=False)
            
            # Process image
            result = ocr.ocr(file_path, cls=True)
            
            # Extract text and confidence
            extracted_text = []
            confidences = []
            
            for idx in range(len(result)):
                res = result[idx]
                if res:
                    for line in res:
                        text = line[1][0]
                        confidence = line[1][1]
                        extracted_text.append(text)
                        confidences.append(confidence)
            
            full_text = '\n'.join(extracted_text)
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0
            
            return {
                'engine': 'paddleocr',
                'success': True,
                'text': full_text,
                'confidence': avg_confidence * 100,  # Convert to percentage
                'processing_time': time.time() - start_time,
                'line_count': len(extracted_text)
            }
            
        except Exception as e:
            return {
                'engine': 'paddleocr',
                'success': False,
                'error': str(e),
                'processing_time': time.time() - start_time if 'start_time' in locals() else 0
            }

    def test_easyocr(self, file_path: str) -> Dict[str, Any]:
        """Test EasyOCR"""
        try:
            import easyocr
            
            start_time = time.time()
            
            # Initialize EasyOCR
            reader = easyocr.Reader(['en'], gpu=False)
            
            # Process image
            result = reader.readtext(file_path)
            
            # Extract text and confidence
            extracted_text = []
            confidences = []
            
            for (bbox, text, confidence) in result:
                extracted_text.append(text)
                confidences.append(confidence)
            
            full_text = '\n'.join(extracted_text)
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0
            
            return {
                'engine': 'easyocr',
                'success': True,
                'text': full_text,
                'confidence': avg_confidence * 100,  # Convert to percentage
                'processing_time': time.time() - start_time,
                'line_count': len(extracted_text)
            }
            
        except Exception as e:
            return {
                'engine': 'easyocr',
                'success': False,
                'error': str(e),
                'processing_time': time.time() - start_time if 'start_time' in locals() else 0
            }

    def test_ocr_space(self, file_path: str) -> Dict[str, Any]:
        """Test OCR.space API"""
        try:
            start_time = time.time()
            
            # Free API key for testing (replace with your own)
            api_key = "helloworld"  # This is the demo key, get your own at ocr.space
            
            url = "https://api.ocr.space/parse/image"
            
            with open(file_path, 'rb') as f:
                files = {'file': f}
                data = {
                    'apikey': api_key,
                    'language': 'eng',
                    'detectOrientation': 'true',
                    'scale': 'true',
                    'isTable': 'true',
                    'OCREngine': '2'  # Use engine 2 for better accuracy
                }
                
                response = requests.post(url, files=files, data=data, timeout=60)
                response.raise_for_status()
                
                result = response.json()
                
                if result.get('OCRExitCode') == 1:
                    parsed_text = result['ParsedResults'][0]['ParsedText']
                    return {
                        'engine': 'ocr_space',
                        'success': True,
                        'text': parsed_text,
                        'processing_time': time.time() - start_time,
                        'api_response': result
                    }
                else:
                    return {
                        'engine': 'ocr_space',
                        'success': False,
                        'error': result.get('ErrorMessage', 'Unknown error'),
                        'processing_time': time.time() - start_time
                    }
                    
        except Exception as e:
            return {
                'engine': 'ocr_space',
                'success': False,
                'error': str(e),
                'processing_time': time.time() - start_time if 'start_time' in locals() else 0
            }

    def test_azure(self, file_path: str) -> Dict[str, Any]:
        """Test Azure Document Intelligence (requires API key)"""
        try:
            # Placeholder - requires Azure credentials
            return {
                'engine': 'azure_doc_intelligence',
                'success': False,
                'error': 'Azure credentials not configured. Please add your endpoint and key.',
                'processing_time': 0,
                'setup_required': True
            }
        except Exception as e:
            return {
                'engine': 'azure_doc_intelligence',
                'success': False,
                'error': str(e),
                'processing_time': 0
            }

    def test_textract(self, file_path: str) -> Dict[str, Any]:
        """Test AWS Textract (requires AWS credentials)"""
        try:
            # Placeholder - requires AWS credentials
            return {
                'engine': 'aws_textract',
                'success': False,
                'error': 'AWS credentials not configured. Please add your AWS access keys.',
                'processing_time': 0,
                'setup_required': True
            }
        except Exception as e:
            return {
                'engine': 'aws_textract',
                'success': False,
                'error': str(e),
                'processing_time': 0
            }

    def test_google_vision(self, file_path: str) -> Dict[str, Any]:
        """Test Google Vision API (requires API key)"""
        try:
            # Placeholder - requires Google credentials
            return {
                'engine': 'google_vision',
                'success': False,
                'error': 'Google Vision credentials not configured. Please add your API key.',
                'processing_time': 0,
                'setup_required': True
            }
        except Exception as e:
            return {
                'engine': 'google_vision',
                'success': False,
                'error': str(e),
                'processing_time': 0
            }

    def run_comprehensive_test(self, engines: List[str] = None) -> Dict[str, Any]:
        """Run comprehensive testing on all engines and documents"""
        if engines is None:
            engines = list(self.engine_configs.keys())
        
        results = {}
        
        for doc_name, doc_url in self.demo_documents.items():
            logger.info(f"Testing document: {doc_name}")
            
            # Download document
            local_file = self.download_demo_file(doc_url, f"{doc_name}.{doc_url.split('.')[-1]}")
            if not local_file:
                continue
            
            results[doc_name] = {}
            
            for engine_name in engines:
                if engine_name in self.engine_configs:
                    logger.info(f"  Testing with {engine_name}")
                    engine_result = self.engine_configs[engine_name](local_file)
                    results[doc_name][engine_name] = engine_result
                    
                    # Print summary
                    if engine_result['success']:
                        logger.info(f"    ✓ Success - {len(engine_result.get('text', ''))} chars extracted")
                    else:
                        logger.info(f"    ✗ Failed - {engine_result.get('error', 'Unknown error')}")
        
        # Save results
        results_file = self.results_dir / "comprehensive_test_results.json"
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"Results saved to {results_file}")
        return results

    def generate_report(self, results: Dict[str, Any]) -> str:
        """Generate a detailed test report"""
        report = []
        report.append("# OCR Engine Comparison Report\n")
        
        # Summary table
        report.append("## Summary\n")
        report.append("| Engine | Success Rate | Avg Processing Time | Notes |")
        report.append("|--------|-------------|--------------------| ------|")
        
        engine_stats = {}
        for doc_name, doc_results in results.items():
            for engine_name, engine_result in doc_results.items():
                if engine_name not in engine_stats:
                    engine_stats[engine_name] = {'success': 0, 'total': 0, 'times': []}
                
                engine_stats[engine_name]['total'] += 1
                if engine_result['success']:
                    engine_stats[engine_name]['success'] += 1
                engine_stats[engine_name]['times'].append(engine_result['processing_time'])
        
        for engine_name, stats in engine_stats.items():
            success_rate = (stats['success'] / stats['total']) * 100
            avg_time = sum(stats['times']) / len(stats['times'])
            
            notes = ""
            if any(results[doc][engine_name].get('setup_required') for doc in results if engine_name in results[doc]):
                notes = "Setup Required"
            
            report.append(f"| {engine_name} | {success_rate:.1f}% | {avg_time:.2f}s | {notes} |")
        
        # Detailed results
        report.append("\n## Detailed Results\n")
        
        for doc_name, doc_results in results.items():
            report.append(f"### Document: {doc_name}\n")
            
            for engine_name, engine_result in doc_results.items():
                report.append(f"#### {engine_name}")
                
                if engine_result['success']:
                    text_preview = engine_result['text'][:200] + "..." if len(engine_result['text']) > 200 else engine_result['text']
                    report.append(f"- **Status**: ✓ Success")
                    report.append(f"- **Processing Time**: {engine_result['processing_time']:.2f}s")
                    report.append(f"- **Text Length**: {len(engine_result['text'])} characters")
                    if 'confidence' in engine_result:
                        report.append(f"- **Confidence**: {engine_result['confidence']:.1f}%")
                    report.append(f"- **Text Preview**: {text_preview}")
                else:
                    report.append(f"- **Status**: ✗ Failed")
                    report.append(f"- **Error**: {engine_result['error']}")
                
                report.append("")
        
        report_text = "\n".join(report)
        
        # Save report
        report_file = self.results_dir / "test_report.md"
        with open(report_file, 'w') as f:
            f.write(report_text)
        
        logger.info(f"Report saved to {report_file}")
        return report_text

if __name__ == "__main__":
    # Install required packages
    print("Installing required packages...")
    os.system("pip install pillow pytesseract opencv-python-headless numpy requests paddleocr easyocr PyMuPDF")
    
    # Initialize tester
    tester = OCRTester()
    
    # Test free/local engines first
    free_engines = ['tesseract', 'paddleocr', 'easyocr', 'ocr_space']
    
    print("\n" + "="*50)
    print("STARTING OCR ENGINE COMPARISON TEST")
    print("="*50)
    
    # Run tests
    results = tester.run_comprehensive_test(free_engines)
    
    # Generate report
    report = tester.generate_report(results)
    
    print("\n" + "="*50)
    print("TEST COMPLETED")
    print("="*50)
    print(f"Results saved in: {tester.results_dir}")
    print("\nQuick Summary:")
    
    # Print quick summary
    for doc_name, doc_results in results.items():
        print(f"\n{doc_name.upper()}:")
        for engine_name, engine_result in doc_results.items():
            status = "✓" if engine_result['success'] else "✗"
            time_info = f"({engine_result['processing_time']:.2f}s)" if engine_result['success'] else ""
            print(f"  {status} {engine_name} {time_info}") 