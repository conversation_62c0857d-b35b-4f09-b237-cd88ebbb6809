#!/usr/bin/env python3
"""
Enterprise-Grade OCR API with FastAPI
Provides high-accuracy text extraction and configuration management
"""

import os
import io
import time
import asyncio
import tempfile
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
import logging
import json
import hashlib
from datetime import datetime

from fastapi import FastAPI, File, UploadFile, HTTPException, Depends, Form, BackgroundTasks
from fastapi.responses import JSONResponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn

# OCR engine imports
try:
    import pytesseract
    from PIL import Image
    import cv2
    import numpy as np
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False

try:
    from paddleocr import PaddleOCR
    PADDLEOCR_AVAILABLE = True
except ImportError:
    PADDLEOCR_AVAILABLE = False

try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    EASYOCR_AVAILABLE = False

try:
    import fitz  # PyMuPDF
    PDF_SUPPORT = True
except ImportError:
    PDF_SUPPORT = False

import requests

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Enterprise OCR API",
    description="High-accuracy OCR service with ensemble processing",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class OCRRequest(BaseModel):
    language: str = Field(default="en", description="Language code (en, fr, de, etc.)")
    engines: List[str] = Field(default=["auto"], description="OCR engines to use")
    enhance_image: bool = Field(default=True, description="Apply image enhancement")
    detect_orientation: bool = Field(default=True, description="Auto-detect and correct orientation")
    extract_tables: bool = Field(default=False, description="Extract table structures")
    confidence_threshold: float = Field(default=0.7, description="Minimum confidence threshold")

class OCRResponse(BaseModel):
    success: bool
    text: str
    confidence: float
    processing_time: float
    engines_used: List[str]
    language_detected: Optional[str] = None
    orientation: Optional[int] = None
    word_count: int
    metadata: Dict[str, Any] = {}

class OCRConfig(BaseModel):
    default_engines: List[str] = ["tesseract", "paddleocr", "easyocr"]
    default_language: str = "en"
    max_file_size: int = 50 * 1024 * 1024  # 50MB
    supported_formats: List[str] = ["jpg", "jpeg", "png", "tiff", "pdf", "bmp"]
    enhance_images: bool = True
    use_gpu: bool = False
    cache_results: bool = True
    confidence_threshold: float = 0.7

class ConfigResponse(BaseModel):
    config: OCRConfig
    available_engines: List[str]
    system_info: Dict[str, Any]

# Global configuration and engine instances
config = OCRConfig()
engine_instances = {}

def initialize_engines():
    """Initialize available OCR engines"""
    global engine_instances
    
    # Initialize Tesseract
    if TESSERACT_AVAILABLE:
        try:
            # Test if tesseract is installed
            pytesseract.get_tesseract_version()
            engine_instances['tesseract'] = True
            logger.info("Tesseract OCR initialized")
        except Exception as e:
            logger.warning(f"Tesseract not available: {e}")
    
    # Initialize PaddleOCR
    if PADDLEOCR_AVAILABLE:
        try:
            engine_instances['paddleocr'] = PaddleOCR(
                use_angle_cls=True, 
                lang='en', 
                use_gpu=config.use_gpu
            )
            logger.info("PaddleOCR initialized")
        except Exception as e:
            logger.warning(f"PaddleOCR not available: {e}")
    
    # Initialize EasyOCR
    if EASYOCR_AVAILABLE:
        try:
            engine_instances['easyocr'] = easyocr.Reader(['en'], gpu=config.use_gpu)
            logger.info("EasyOCR initialized")
        except Exception as e:
            logger.warning(f"EasyOCR not available: {e}")
    
    # OCR.space is API-based, no initialization needed
    engine_instances['ocr_space'] = True
    
    logger.info(f"Initialized engines: {list(engine_instances.keys())}")

class OCRProcessor:
    """Advanced OCR processing with ensemble methods"""
    
    def __init__(self):
        self.temp_dir = Path(tempfile.gettempdir()) / "ocr_api"
        self.temp_dir.mkdir(exist_ok=True)
    
    def preprocess_image(self, image: Image.Image, enhance: bool = True) -> Image.Image:
        """Apply image preprocessing for better OCR accuracy"""
        if not enhance:
            return image
        
        # Convert PIL to OpenCV
        img_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
        
        # Convert to grayscale
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        
        # Apply denoising
        denoised = cv2.fastNlMeansDenoising(gray)
        
        # Apply adaptive thresholding for better text contrast
        binary = cv2.adaptiveThreshold(
            denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        
        # Convert back to PIL
        return Image.fromarray(binary)
    
    def detect_orientation(self, image: Image.Image) -> tuple:
        """Detect and correct image orientation"""
        if not TESSERACT_AVAILABLE:
            return image, 0
        
        try:
            # Use Tesseract's orientation detection
            osd = pytesseract.image_to_osd(image)
            angle = 0
            for line in osd.split('\n'):
                if 'Rotate:' in line:
                    angle = int(line.split(':')[1].strip())
                    break
            
            if angle != 0:
                # Rotate image
                image = image.rotate(-angle, expand=True)
            
            return image, angle
        except Exception as e:
            logger.warning(f"Orientation detection failed: {e}")
            return image, 0
    
    def extract_text_tesseract(self, image: Image.Image, language: str = "eng") -> Dict[str, Any]:
        """Extract text using Tesseract OCR"""
        if not TESSERACT_AVAILABLE:
            raise Exception("Tesseract not available")
        
        start_time = time.time()
        
        # Try multiple PSM configurations for best results
        configs = [
            f'--oem 3 --psm 6 -l {language}',  # Uniform block
            f'--oem 3 --psm 4 -l {language}',  # Single column
            f'--oem 3 --psm 3 -l {language}',  # Fully automatic
        ]
        
        best_result = None
        best_confidence = 0
        
        for config in configs:
            try:
                text = pytesseract.image_to_string(image, config=config)
                data = pytesseract.image_to_data(
                    image, config=config, output_type=pytesseract.Output.DICT
                )
                
                # Calculate average confidence
                confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
                avg_confidence = np.mean(confidences) if confidences else 0
                
                if avg_confidence > best_confidence:
                    best_confidence = avg_confidence
                    best_result = {
                        'text': text.strip(),
                        'confidence': avg_confidence,
                        'config': config
                    }
            except Exception as e:
                logger.warning(f"Tesseract config {config} failed: {e}")
                continue
        
        if best_result is None:
            raise Exception("All Tesseract configurations failed")
        
        return {
            'engine': 'tesseract',
            'text': best_result['text'],
            'confidence': best_result['confidence'] / 100.0,  # Normalize to 0-1
            'processing_time': time.time() - start_time,
            'metadata': {'config_used': best_result['config']}
        }
    
    def extract_text_paddleocr(self, image_path: str, language: str = "en") -> Dict[str, Any]:
        """Extract text using PaddleOCR"""
        if 'paddleocr' not in engine_instances:
            raise Exception("PaddleOCR not available")
        
        start_time = time.time()
        ocr = engine_instances['paddleocr']
        
        # Process image
        result = ocr.ocr(image_path, cls=True)
        
        # Extract text and confidence
        extracted_text = []
        confidences = []
        
        for idx in range(len(result)):
            res = result[idx]
            if res:
                for line in res:
                    text = line[1][0]
                    confidence = line[1][1]
                    extracted_text.append(text)
                    confidences.append(confidence)
        
        full_text = '\n'.join(extracted_text)
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0
        
        return {
            'engine': 'paddleocr',
            'text': full_text,
            'confidence': avg_confidence,
            'processing_time': time.time() - start_time,
            'metadata': {'lines_detected': len(extracted_text)}
        }
    
    def extract_text_easyocr(self, image_path: str, language: str = "en") -> Dict[str, Any]:
        """Extract text using EasyOCR"""
        if 'easyocr' not in engine_instances:
            raise Exception("EasyOCR not available")
        
        start_time = time.time()
        reader = engine_instances['easyocr']
        
        # Process image
        result = reader.readtext(image_path)
        
        # Extract text and confidence
        extracted_text = []
        confidences = []
        
        for (bbox, text, confidence) in result:
            extracted_text.append(text)
            confidences.append(confidence)
        
        full_text = '\n'.join(extracted_text)
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0
        
        return {
            'engine': 'easyocr',
            'text': full_text,
            'confidence': avg_confidence,
            'processing_time': time.time() - start_time,
            'metadata': {'lines_detected': len(extracted_text)}
        }
    
    def extract_text_ocr_space(self, image_path: str, language: str = "eng") -> Dict[str, Any]:
        """Extract text using OCR.space API"""
        start_time = time.time()
        
        # Use demo API key (replace with your own for production)
        api_key = "helloworld"
        url = "https://api.ocr.space/parse/image"
        
        with open(image_path, 'rb') as f:
            files = {'file': f}
            data = {
                'apikey': api_key,
                'language': language,
                'detectOrientation': 'true',
                'scale': 'true',
                'isTable': 'true',
                'OCREngine': '2'
            }
            
            response = requests.post(url, files=files, data=data, timeout=60)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get('OCRExitCode') == 1:
                text = result['ParsedResults'][0]['ParsedText']
                return {
                    'engine': 'ocr_space',
                    'text': text,
                    'confidence': 0.9,  # OCR.space doesn't provide confidence
                    'processing_time': time.time() - start_time,
                    'metadata': result
                }
            else:
                raise Exception(f"OCR.space failed: {result.get('ErrorMessage', 'Unknown error')}")
    
    def process_with_ensemble(self, image: Image.Image, temp_path: str, engines: List[str], language: str) -> Dict[str, Any]:
        """Process image with multiple engines and combine results"""
        results = []
        available_engines = list(engine_instances.keys())
        
        # If "auto" is specified, use all available engines
        if "auto" in engines:
            engines = available_engines
        
        # Filter engines to only available ones
        engines = [eng for eng in engines if eng in available_engines]
        
        if not engines:
            raise HTTPException(status_code=400, detail="No available OCR engines")
        
        # Run each engine
        for engine in engines:
            try:
                if engine == "tesseract":
                    result = self.extract_text_tesseract(image, language)
                elif engine == "paddleocr":
                    result = self.extract_text_paddleocr(temp_path, language)
                elif engine == "easyocr":
                    result = self.extract_text_easyocr(temp_path, language)
                elif engine == "ocr_space":
                    result = self.extract_text_ocr_space(temp_path, language)
                else:
                    continue
                
                results.append(result)
                logger.info(f"Engine {engine}: {result['confidence']:.2f} confidence, {len(result['text'])} chars")
                
            except Exception as e:
                logger.warning(f"Engine {engine} failed: {e}")
                continue
        
        if not results:
            raise HTTPException(status_code=500, detail="All OCR engines failed")
        
        # Ensemble method: Use result with highest confidence
        best_result = max(results, key=lambda x: x['confidence'])
        
        # If confidence is very low, try combining results
        if best_result['confidence'] < config.confidence_threshold and len(results) > 1:
            # Simple ensemble: combine texts and average confidence
            combined_text = best_result['text']  # Start with best result
            combined_confidence = sum(r['confidence'] for r in results) / len(results)
            
            best_result = {
                'engine': 'ensemble',
                'text': combined_text,
                'confidence': combined_confidence,
                'processing_time': sum(r['processing_time'] for r in results),
                'metadata': {'individual_results': results}
            }
        
        return best_result, [r['engine'] for r in results]

# Initialize OCR processor
processor = OCRProcessor()

@app.on_event("startup")
async def startup_event():
    """Initialize engines on startup"""
    initialize_engines()
    logger.info("OCR API started successfully")

@app.get("/", response_class=JSONResponse)
async def root():
    """API root endpoint"""
    return {
        "message": "Enterprise OCR API",
        "version": "1.0.0",
        "available_engines": list(engine_instances.keys()),
        "docs": "/docs"
    }

@app.get("/health", response_class=JSONResponse)
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "available_engines": list(engine_instances.keys())
    }

@app.post("/extract-text", response_model=OCRResponse)
async def extract_text(
    file: UploadFile = File(..., description="Document file (PDF, image)"),
    language: str = Form(default="en", description="Language code"),
    engines: str = Form(default="auto", description="Comma-separated engine names"),
    enhance_image: bool = Form(default=True, description="Apply image enhancement"),
    detect_orientation: bool = Form(default=True, description="Auto-detect orientation"),
    confidence_threshold: float = Form(default=0.7, description="Minimum confidence threshold")
):
    """
    Extract text from uploaded document with high accuracy
    
    Returns raw text with 100% accuracy goal using ensemble OCR processing
    """
    start_time = time.time()
    temp_path = None
    
    try:
        # Validate file
        if file.size > config.max_file_size:
            raise HTTPException(status_code=413, detail="File too large")
        
        file_ext = file.filename.split('.')[-1].lower()
        if file_ext not in config.supported_formats:
            raise HTTPException(status_code=400, detail=f"Unsupported format: {file_ext}")
        
        # Parse engines list
        engine_list = [eng.strip() for eng in engines.split(',')]
        
        # Read file content
        content = await file.read()
        
        # Create temporary file
        temp_path = processor.temp_dir / f"upload_{int(time.time())}_{file.filename}"
        with open(temp_path, 'wb') as f:
            f.write(content)
        
        # Process based on file type
        if file_ext == 'pdf':
            if not PDF_SUPPORT:
                raise HTTPException(status_code=400, detail="PDF support not available")
            
            # Extract first page as image
            doc = fitz.open(str(temp_path))
            page = doc[0]
            pix = page.get_pixmap(matrix=fitz.Matrix(2.0, 2.0))  # 2x resolution
            image = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
            doc.close()
        else:
            # Load image
            image = Image.open(temp_path)
            
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
        
        # Apply preprocessing
        if enhance_image:
            image = processor.preprocess_image(image, enhance=True)
        
        # Detect and correct orientation
        orientation = 0
        if detect_orientation:
            image, orientation = processor.detect_orientation(image)
        
        # Save processed image for engines that need file path
        processed_path = processor.temp_dir / f"processed_{int(time.time())}.png"
        image.save(processed_path)
        
        try:
            # Process with ensemble
            result, engines_used = processor.process_with_ensemble(
                image, str(processed_path), engine_list, language
            )
            
            # Calculate final metrics
            word_count = len(result['text'].split()) if result['text'] else 0
            total_time = time.time() - start_time
            
            response = OCRResponse(
                success=True,
                text=result['text'],
                confidence=result['confidence'],
                processing_time=total_time,
                engines_used=engines_used,
                orientation=orientation if orientation != 0 else None,
                word_count=word_count,
                metadata={
                    'file_size': len(content),
                    'file_type': file_ext,
                    'image_size': f"{image.width}x{image.height}",
                    'enhanced': enhance_image,
                    'engine_details': result.get('metadata', {})
                }
            )
            
            logger.info(f"OCR completed: {word_count} words, {result['confidence']:.2f} confidence")
            return response
            
        finally:
            # Cleanup processed image
            if processed_path.exists():
                processed_path.unlink()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"OCR processing failed: {e}")
        raise HTTPException(status_code=500, detail=f"OCR processing failed: {str(e)}")
    
    finally:
        # Cleanup temporary file
        if temp_path and temp_path.exists():
            temp_path.unlink()

@app.get("/config", response_model=ConfigResponse)
async def get_config():
    """Get current OCR configuration and system information"""
    system_info = {
        'available_engines': list(engine_instances.keys()),
        'tesseract_available': TESSERACT_AVAILABLE,
        'paddleocr_available': PADDLEOCR_AVAILABLE,
        'easyocr_available': EASYOCR_AVAILABLE,
        'pdf_support': PDF_SUPPORT,
        'gpu_enabled': config.use_gpu
    }
    
    if TESSERACT_AVAILABLE:
        try:
            system_info['tesseract_version'] = pytesseract.get_tesseract_version()
        except:
            pass
    
    return ConfigResponse(
        config=config,
        available_engines=list(engine_instances.keys()),
        system_info=system_info
    )

@app.post("/config", response_model=ConfigResponse)
async def update_config(new_config: OCRConfig):
    """Update OCR configuration"""
    global config
    
    # Validate configuration
    if new_config.confidence_threshold < 0 or new_config.confidence_threshold > 1:
        raise HTTPException(status_code=400, detail="Confidence threshold must be between 0 and 1")
    
    if new_config.max_file_size <= 0:
        raise HTTPException(status_code=400, detail="Max file size must be positive")
    
    # Update global config
    config = new_config
    
    # Reinitialize engines if GPU setting changed
    if new_config.use_gpu != config.use_gpu:
        initialize_engines()
    
    logger.info("Configuration updated")
    return await get_config()

@app.get("/engines", response_class=JSONResponse)
async def list_engines():
    """List available OCR engines with their status"""
    engines_status = {}
    
    for engine_name in ['tesseract', 'paddleocr', 'easyocr', 'ocr_space']:
        engines_status[engine_name] = {
            'available': engine_name in engine_instances,
            'description': {
                'tesseract': 'Google Tesseract OCR - Best for printed text',
                'paddleocr': 'PaddleOCR - Excellent for multilingual and complex layouts',
                'easyocr': 'EasyOCR - Good balance of speed and accuracy',
                'ocr_space': 'OCR.space API - Cloud-based OCR service'
            }.get(engine_name, 'OCR Engine')
        }
    
    return {
        'engines': engines_status,
        'total_available': len(engine_instances),
        'recommended_ensemble': list(engine_instances.keys())[:3]  # Top 3 engines
    }

if __name__ == "__main__":
    uvicorn.run(
        "ocr_api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    ) 