# Install required packages
!pip install pytesseract pillow opencv-python pandas numpy
!pip install paddlepaddle paddleocr
!pip install easyocr
!pip install transformers torch torchvision
!pip install azure-ai-documentintelligence azure-core
!pip install surya-ocr
!pip install pdf2image
!pip install openpyxl xlrd
!pip install matplotlib seaborn plotly

import os
import time
import pandas as pd
import numpy as np
from PIL import Image
import cv2
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Set up paths
DATA_PATH = Path("Data Docs")
RESULTS_PATH = Path("ocr_results")
RESULTS_PATH.mkdir(exist_ok=True)

print(f"Data path: {DATA_PATH}")
print(f"Results path: {RESULTS_PATH}")
print(f"Available test files: {list(DATA_PATH.glob('*'))}")

class OCREngine:
    """Base class for OCR engines"""
    def __init__(self, name):
        self.name = name
        
    def extract_text(self, image_path):
        """Extract text from image. Returns (text, confidence, processing_time)"""
        raise NotImplementedError
        
    def preprocess_image(self, image_path):
        """Common image preprocessing"""
        img = cv2.imread(str(image_path))
        if img is None:
            # Try with PIL for different formats
            img = np.array(Image.open(image_path))
            if len(img.shape) == 3:
                img = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
        return img

class TesseractOCR(OCREngine):
    def __init__(self):
        super().__init__("Tesseract")
        import pytesseract
        self.tesseract = pytesseract
        
    def extract_text(self, image_path):
        start_time = time.time()
        try:
            img = self.preprocess_image(image_path)
            
            # Get text with confidence
            data = self.tesseract.image_to_data(img, output_type=self.tesseract.Output.DICT)
            text = self.tesseract.image_to_string(img)
            
            # Calculate average confidence
            confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
            avg_confidence = np.mean(confidences) if confidences else 0
            
            processing_time = time.time() - start_time
            return text.strip(), avg_confidence, processing_time
            
        except Exception as e:
            processing_time = time.time() - start_time
            return f"Error: {str(e)}", 0, processing_time

class PaddleOCR_Engine(OCREngine):
    def __init__(self):
        super().__init__("PaddleOCR")
        from paddleocr import PaddleOCR
        self.ocr = PaddleOCR(use_angle_cls=True, lang='en', show_log=False)
        
    def extract_text(self, image_path):
        start_time = time.time()
        try:
            result = self.ocr.ocr(str(image_path), cls=True)
            
            if result and result[0]:
                texts = []
                confidences = []
                
                for line in result[0]:
                    if line and len(line) >= 2:
                        text = line[1][0]
                        confidence = line[1][1]
                        texts.append(text)
                        confidences.append(confidence)
                
                full_text = '\n'.join(texts)
                avg_confidence = np.mean(confidences) * 100 if confidences else 0
            else:
                full_text = ""
                avg_confidence = 0
                
            processing_time = time.time() - start_time
            return full_text, avg_confidence, processing_time
            
        except Exception as e:
            processing_time = time.time() - start_time
            return f"Error: {str(e)}", 0, processing_time

class EasyOCR_Engine(OCREngine):
    def __init__(self):
        super().__init__("EasyOCR")
        import easyocr
        self.reader = easyocr.Reader(['en'], gpu=True if torch.cuda.is_available() else False)
        
    def extract_text(self, image_path):
        start_time = time.time()
        try:
            results = self.reader.readtext(str(image_path))
            
            texts = []
            confidences = []
            
            for (bbox, text, confidence) in results:
                texts.append(text)
                confidences.append(confidence)
            
            full_text = '\n'.join(texts)
            avg_confidence = np.mean(confidences) * 100 if confidences else 0
            
            processing_time = time.time() - start_time
            return full_text, avg_confidence, processing_time
            
        except Exception as e:
            processing_time = time.time() - start_time
            return f"Error: {str(e)}", 0, processing_time

class TrOCR_Engine(OCREngine):
    def __init__(self):
        super().__init__("TrOCR")
        from transformers import TrOCRProcessor, VisionEncoderDecoderModel
        
        self.processor = TrOCRProcessor.from_pretrained("microsoft/trocr-base-printed")
        self.model = VisionEncoderDecoderModel.from_pretrained("microsoft/trocr-base-printed")
        
    def extract_text(self, image_path):
        start_time = time.time()
        try:
            image = Image.open(image_path).convert('RGB')
            
            # Process the image
            pixel_values = self.processor(image, return_tensors="pt").pixel_values
            generated_ids = self.model.generate(pixel_values)
            generated_text = self.processor.batch_decode(generated_ids, skip_special_tokens=True)[0]
            
            processing_time = time.time() - start_time
            # TrOCR doesn't provide confidence scores, so we'll use a default
            return generated_text, 85.0, processing_time
            
        except Exception as e:
            processing_time = time.time() - start_time
            return f"Error: {str(e)}", 0, processing_time

class SuryaOCR_Engine(OCREngine):
    def __init__(self):
        super().__init__("Surya")
        try:
            from surya.ocr import run_ocr
            from surya.model.detection.model import load_model as load_det_model, load_processor as load_det_processor
            from surya.model.recognition.model import load_model as load_rec_model
            from surya.model.recognition.processor import load_processor as load_rec_processor
            
            self.det_processor, self.det_model = load_det_processor(), load_det_model()
            self.rec_model, self.rec_processor = load_rec_model(), load_rec_processor()
            self.run_ocr = run_ocr
            self.available = True
        except ImportError:
            self.available = False
            print("Surya OCR not available. Install with: pip install surya-ocr")
        
    def extract_text(self, image_path):
        start_time = time.time()
        if not self.available:
            return "Surya OCR not installed", 0, 0
            
        try:
            image = Image.open(image_path)
            predictions = self.run_ocr([image], [['en']], self.det_model, self.det_processor, 
                                     self.rec_model, self.rec_processor)
            
            if predictions:
                text_lines = []
                confidences = []
                
                for text_line in predictions[0].text_lines:
                    text_lines.append(text_line.text)
                    confidences.append(text_line.confidence if hasattr(text_line, 'confidence') else 0.8)
                
                full_text = '\n'.join(text_lines)
                avg_confidence = np.mean(confidences) * 100 if confidences else 0
            else:
                full_text = ""
                avg_confidence = 0
                
            processing_time = time.time() - start_time
            return full_text, avg_confidence, processing_time
            
        except Exception as e:
            processing_time = time.time() - start_time
            return f"Error: {str(e)}", 0, processing_time

def convert_pdf_to_images(pdf_path, output_dir):
    """Convert PDF to images for OCR processing"""
    try:
        from pdf2image import convert_from_path
        
        output_dir = Path(output_dir)
        output_dir.mkdir(exist_ok=True)
        
        images = convert_from_path(pdf_path, dpi=300)
        image_paths = []
        
        for i, image in enumerate(images):
            image_path = output_dir / f"{Path(pdf_path).stem}_page_{i+1}.png"
            image.save(image_path, 'PNG')
            image_paths.append(image_path)
            
        return image_paths
        
    except Exception as e:
        print(f"Error converting PDF {pdf_path}: {e}")
        return []

def process_excel_file(excel_path):
    """Process Excel file and extract text content"""
    try:
        # Try different engines
        for engine in ['openpyxl', 'xlrd']:
            try:
                df = pd.read_excel(excel_path, engine=engine)
                # Convert all data to string and combine
                text_content = df.to_string(index=False)
                return text_content
            except:
                continue
        return "Could not read Excel file"
    except Exception as e:
        return f"Error reading Excel: {e}"

def test_all_engines():
    """Test all OCR engines on available documents"""
    
    # Initialize engines
    engines = [
        TesseractOCR(),
        PaddleOCR_Engine(),
        EasyOCR_Engine(),
        TrOCR_Engine(),
        SuryaOCR_Engine()
    ]
    
    results = []
    
    # Get all test files
    test_files = list(DATA_PATH.glob('*'))
    
    for file_path in test_files:
        print(f"\nProcessing: {file_path.name}")
        
        if file_path.suffix.lower() == '.pdf':
            # Convert PDF to images
            temp_dir = RESULTS_PATH / "temp_images"
            image_paths = convert_pdf_to_images(file_path, temp_dir)
            
            for img_path in image_paths:
                for engine in engines:
                    print(f"  Testing {engine.name} on {img_path.name}...")
                    text, confidence, proc_time = engine.extract_text(img_path)
                    
                    results.append({
                        'file': file_path.name,
                        'page': img_path.name,
                        'engine': engine.name,
                        'text': text,
                        'confidence': confidence,
                        'processing_time': proc_time,
                        'text_length': len(text),
                        'timestamp': datetime.now().isoformat()
                    })
                    
        elif file_path.suffix.lower() in ['.png', '.jpg', '.jpeg', '.tiff', '.bmp']:
            # Process image directly
            for engine in engines:
                print(f"  Testing {engine.name}...")
                text, confidence, proc_time = engine.extract_text(file_path)
                
                results.append({
                    'file': file_path.name,
                    'page': 'N/A',
                    'engine': engine.name,
                    'text': text,
                    'confidence': confidence,
                    'processing_time': proc_time,
                    'text_length': len(text),
                    'timestamp': datetime.now().isoformat()
                })
                
        elif file_path.suffix.lower() in ['.xlsx', '.xls']:
            # Process Excel file
            print(f"  Processing Excel file...")
            text = process_excel_file(file_path)
            
            results.append({
                'file': file_path.name,
                'page': 'N/A',
                'engine': 'Excel_Reader',
                'text': text,
                'confidence': 100.0,
                'processing_time': 0.1,
                'text_length': len(text),
                'timestamp': datetime.now().isoformat()
            })
    
    return pd.DataFrame(results)

# Import required libraries
import torch
print(f"CUDA available: {torch.cuda.is_available()}")
print(f"PyTorch version: {torch.__version__}")

# Run the tests
print("Starting OCR engine testing...")
results_df = test_all_engines()

# Save results
results_file = RESULTS_PATH / f"ocr_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
results_df.to_csv(results_file, index=False)
print(f"\nResults saved to: {results_file}")

# Display summary
print(f"\nTotal tests run: {len(results_df)}")
print(f"Engines tested: {results_df['engine'].unique()}")
print(f"Files processed: {results_df['file'].unique()}")

# Performance Analysis
def analyze_results(df):
    """Analyze OCR engine performance"""
    
    print("=== OCR ENGINE PERFORMANCE ANALYSIS ===")
    print()
    
    # Summary by engine
    engine_summary = df.groupby('engine').agg({
        'confidence': ['mean', 'std'],
        'processing_time': ['mean', 'std'],
        'text_length': ['mean', 'std'],
        'file': 'count'
    }).round(2)
    
    engine_summary.columns = ['Avg_Confidence', 'Std_Confidence', 'Avg_Time', 'Std_Time', 
                             'Avg_Text_Length', 'Std_Text_Length', 'Total_Tests']
    
    print("Engine Performance Summary:")
    print(engine_summary)
    print()
    
    # Best performing engine by metric
    best_confidence = engine_summary['Avg_Confidence'].idxmax()
    best_speed = engine_summary['Avg_Time'].idxmin()
    best_text_extraction = engine_summary['Avg_Text_Length'].idxmax()
    
    print(f"Best Confidence: {best_confidence} ({engine_summary.loc[best_confidence, 'Avg_Confidence']:.1f}%)")
    print(f"Fastest Processing: {best_speed} ({engine_summary.loc[best_speed, 'Avg_Time']:.2f}s)")
    print(f"Most Text Extracted: {best_text_extraction} ({engine_summary.loc[best_text_extraction, 'Avg_Text_Length']:.0f} chars)")
    print()
    
    return engine_summary

# Run analysis
if len(results_df) > 0:
    summary = analyze_results(results_df)
else:
    print("No results to analyze yet. Run the tests first.")

# Visualization
def create_visualizations(df):
    """Create performance visualizations"""
    
    if len(df) == 0:
        print("No data to visualize")
        return
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. Confidence by Engine
    sns.boxplot(data=df, x='engine', y='confidence', ax=axes[0,0])
    axes[0,0].set_title('Confidence Scores by Engine')
    axes[0,0].set_ylabel('Confidence (%)')
    axes[0,0].tick_params(axis='x', rotation=45)
    
    # 2. Processing Time by Engine
    sns.boxplot(data=df, x='engine', y='processing_time', ax=axes[0,1])
    axes[0,1].set_title('Processing Time by Engine')
    axes[0,1].set_ylabel('Time (seconds)')
    axes[0,1].tick_params(axis='x', rotation=45)
    
    # 3. Text Length by Engine
    sns.boxplot(data=df, x='engine', y='text_length', ax=axes[1,0])
    axes[1,0].set_title('Text Length Extracted by Engine')
    axes[1,0].set_ylabel('Characters')
    axes[1,0].tick_params(axis='x', rotation=45)
    
    # 4. Confidence vs Processing Time
    for engine in df['engine'].unique():
        engine_data = df[df['engine'] == engine]
        axes[1,1].scatter(engine_data['processing_time'], engine_data['confidence'], 
                         label=engine, alpha=0.7)
    axes[1,1].set_xlabel('Processing Time (seconds)')
    axes[1,1].set_ylabel('Confidence (%)')
    axes[1,1].set_title('Confidence vs Processing Time')
    axes[1,1].legend()
    
    plt.tight_layout()
    
    # Save plot
    plot_file = RESULTS_PATH / f"ocr_performance_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
    plt.savefig(plot_file, dpi=300, bbox_inches='tight')
    print(f"Visualization saved to: {plot_file}")
    
    plt.show()

# Create visualizations
if len(results_df) > 0:
    create_visualizations(results_df)

# Detailed Results Inspection
def inspect_results(df, file_name=None, engine_name=None):
    """Inspect detailed results for specific files or engines"""
    
    filtered_df = df.copy()
    
    if file_name:
        filtered_df = filtered_df[filtered_df['file'] == file_name]
        
    if engine_name:
        filtered_df = filtered_df[filtered_df['engine'] == engine_name]
    
    if len(filtered_df) == 0:
        print("No results found for the specified criteria")
        return
    
    print(f"=== DETAILED RESULTS ({len(filtered_df)} entries) ===")
    
    for idx, row in filtered_df.iterrows():
        print(f"\nFile: {row['file']} | Engine: {row['engine']}")
        print(f"Confidence: {row['confidence']:.1f}% | Time: {row['processing_time']:.2f}s | Length: {row['text_length']} chars")
        print(f"Text Preview: {row['text'][:200]}..." if len(row['text']) > 200 else f"Text: {row['text']}")
        print("-" * 80)

# Example usage - inspect specific file results
if len(results_df) > 0:
    print("Available files for inspection:")
    for file in results_df['file'].unique():
        print(f"  - {file}")
    
    # Uncomment and modify to inspect specific results:
    # inspect_results(results_df, file_name="1-3_receipts.pdf")
    # inspect_results(results_df, engine_name="PaddleOCR")

# Export detailed results for manual review
def export_detailed_results(df):
    """Export results in different formats for review"""
    
    if len(df) == 0:
        print("No results to export")
        return
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # 1. CSV export (already done above)
    
    # 2. JSON export with full text
    json_file = RESULTS_PATH / f"ocr_results_detailed_{timestamp}.json"
    df.to_json(json_file, orient='records', indent=2)
    print(f"Detailed JSON results saved to: {json_file}")
    
    # 3. Summary Excel file
    excel_file = RESULTS_PATH / f"ocr_summary_{timestamp}.xlsx"
    
    with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
        # Summary sheet
        summary = df.groupby('engine').agg({
            'confidence': ['mean', 'std', 'min', 'max'],
            'processing_time': ['mean', 'std', 'min', 'max'],
            'text_length': ['mean', 'std', 'min', 'max'],
            'file': 'count'
        }).round(3)
        summary.to_excel(writer, sheet_name='Summary')
        
        # Detailed results (truncated text for readability)
        export_df = df.copy()
        export_df['text_preview'] = export_df['text'].str[:500] + '...'
        export_df.drop('text', axis=1).to_excel(writer, sheet_name='Results', index=False)
        
        # Per-file comparison
        for file in df['file'].unique():
            file_df = df[df['file'] == file][['engine', 'confidence', 'processing_time', 'text_length']]
            safe_filename = file.replace('.', '_').replace(' ', '_')[:30]
            file_df.to_excel(writer, sheet_name=f'File_{safe_filename}', index=False)
    
    print(f"Excel summary saved to: {excel_file}")
    
    # 4. Text files with extracted content
    text_dir = RESULTS_PATH / f"extracted_texts_{timestamp}"
    text_dir.mkdir(exist_ok=True)
    
    for idx, row in df.iterrows():
        filename = f"{row['file']}_{row['engine']}.txt".replace(' ', '_').replace('/', '_')
        text_file = text_dir / filename
        
        with open(text_file, 'w', encoding='utf-8') as f:
            f.write(f"File: {row['file']}\n")
            f.write(f"Engine: {row['engine']}\n")
            f.write(f"Confidence: {row['confidence']:.1f}%\n")
            f.write(f"Processing Time: {row['processing_time']:.2f}s\n")
            f.write(f"Text Length: {row['text_length']} characters\n")
            f.write("=" * 50 + "\n")
            f.write(row['text'])
    
    print(f"Individual text files saved to: {text_dir}")

# Export results
if len(results_df) > 0:
    export_detailed_results(results_df)
    print("\n=== TESTING COMPLETE ===")
    print(f"Check the '{RESULTS_PATH}' directory for all output files.")
else:
    print("Run the tests first to generate results for export.")