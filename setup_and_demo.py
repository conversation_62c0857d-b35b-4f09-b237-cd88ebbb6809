#!/usr/bin/env python3
"""
OCR API Setup and Demo Script
Installs dependencies, tests engines, and demonstrates the API
"""

import os
import sys
import subprocess
import time
import requests
import json
from pathlib import Path

def run_command(command, description):
    """Run a shell command with error handling"""
    print(f"\n{'='*60}")
    print(f"📋 {description}")
    print(f"{'='*60}")
    print(f"Command: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print("✅ Success!")
        if result.stdout:
            print(f"Output: {result.stdout[:500]}...")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed: {e}")
        if e.stderr:
            print(f"Error: {e.stderr}")
        return False

def install_system_dependencies():
    """Install system-level dependencies"""
    print("\n🚀 INSTALLING SYSTEM DEPENDENCIES")
    
    # Detect OS
    if sys.platform.startswith('linux'):
        # Ubuntu/Debian
        commands = [
            "sudo apt update",
            "sudo apt install -y tesseract-ocr tesseract-ocr-eng",
            "sudo apt install -y libtesseract-dev",
            "sudo apt install -y python3-opencv",
        ]
        for cmd in commands:
            run_command(cmd, f"Installing Linux dependencies: {cmd}")
    
    elif sys.platform == 'darwin':
        # macOS
        commands = [
            "brew install tesseract",
            "brew install opencv",
        ]
        for cmd in commands:
            run_command(cmd, f"Installing macOS dependencies: {cmd}")
    
    elif sys.platform == 'win32':
        # Windows
        print("⚠️  Windows detected. Please install:")
        print("1. Tesseract: https://github.com/UB-Mannheim/tesseract/wiki")
        print("2. Add Tesseract to your PATH")
        print("3. Install Visual C++ Build Tools")
        input("Press Enter when ready to continue...")

def install_python_dependencies():
    """Install Python dependencies"""
    print("\n📦 INSTALLING PYTHON DEPENDENCIES")
    
    commands = [
        "pip install --upgrade pip",
        "pip install -r requirements.txt",
    ]
    
    for cmd in commands:
        run_command(cmd, f"Installing Python packages: {cmd}")

def test_individual_engines():
    """Test individual OCR engines"""
    print("\n🔬 TESTING INDIVIDUAL OCR ENGINES")
    
    # Run the OCR testing script
    run_command("python ocr_testing_setup.py", "Running comprehensive OCR engine tests")

def start_api_server():
    """Start the FastAPI server in background"""
    print("\n🌐 STARTING OCR API SERVER")
    
    # Start server in background
    import subprocess
    import threading
    
    def run_server():
        subprocess.run([
            "python", "-m", "uvicorn", "ocr_api:app", 
            "--host", "0.0.0.0", 
            "--port", "8000",
            "--reload"
        ])
    
    server_thread = threading.Thread(target=run_server, daemon=True)
    server_thread.start()
    
    # Wait for server to start
    print("⏳ Waiting for server to start...")
    time.sleep(10)
    
    # Test if server is running
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ API server is running!")
            return True
        else:
            print(f"❌ Server health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Failed to connect to server: {e}")
        return False

def demo_api_endpoints():
    """Demonstrate API endpoints"""
    print("\n🎯 DEMONSTRATING API ENDPOINTS")
    
    base_url = "http://localhost:8000"
    
    # Test basic endpoints
    endpoints = [
        ("/", "Root endpoint"),
        ("/health", "Health check"),
        ("/engines", "Available engines"),
        ("/config", "Current configuration"),
    ]
    
    for endpoint, description in endpoints:
        print(f"\n📌 Testing {description}: {endpoint}")
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            if response.status_code == 200:
                print("✅ Success!")
                print(f"Response: {json.dumps(response.json(), indent=2)}")
            else:
                print(f"❌ Failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Error: {e}")

def demo_ocr_extraction():
    """Demonstrate OCR text extraction with sample images"""
    print("\n📄 DEMONSTRATING OCR TEXT EXTRACTION")
    
    # Download a sample image for testing
    sample_url = "https://ocr.space/Content/Images/receipt-ocr-original.jpg"
    sample_path = "demo_receipt.jpg"
    
    print(f"📥 Downloading sample image: {sample_url}")
    try:
        response = requests.get(sample_url, timeout=30)
        response.raise_for_status()
        
        with open(sample_path, 'wb') as f:
            f.write(response.content)
        print("✅ Sample image downloaded!")
        
        # Test OCR extraction
        print("\n🔍 Testing OCR extraction...")
        
        with open(sample_path, 'rb') as f:
            files = {'file': f}
            data = {
                'language': 'en',
                'engines': 'auto',
                'enhance_image': True,
                'detect_orientation': True
            }
            
            response = requests.post(
                "http://localhost:8000/extract-text",
                files=files,
                data=data,
                timeout=120
            )
            
            if response.status_code == 200:
                result = response.json()
                print("✅ OCR extraction successful!")
                print(f"📊 Results:")
                print(f"   • Success: {result['success']}")
                print(f"   • Confidence: {result['confidence']:.2f}")
                print(f"   • Processing Time: {result['processing_time']:.2f}s")
                print(f"   • Engines Used: {result['engines_used']}")
                print(f"   • Word Count: {result['word_count']}")
                print(f"   • Text Preview: {result['text'][:200]}...")
            else:
                print(f"❌ OCR extraction failed: {response.status_code}")
                print(f"Error: {response.text}")
        
        # Cleanup
        if os.path.exists(sample_path):
            os.remove(sample_path)
            
    except Exception as e:
        print(f"❌ Demo failed: {e}")

def create_client_examples():
    """Create client example scripts"""
    print("\n📝 CREATING CLIENT EXAMPLES")
    
    # Python client example
    python_client = '''#!/usr/bin/env python3
"""
Python client example for OCR API
"""

import requests
import json

def extract_text_from_file(file_path, language="en", engines="auto"):
    """Extract text from a file using the OCR API"""
    
    url = "http://localhost:8000/extract-text"
    
    with open(file_path, 'rb') as f:
        files = {'file': f}
        data = {
            'language': language,
            'engines': engines,
            'enhance_image': True,
            'detect_orientation': True,
            'confidence_threshold': 0.7
        }
        
        response = requests.post(url, files=files, data=data)
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"OCR failed: {response.status_code} - {response.text}")

def get_available_engines():
    """Get list of available OCR engines"""
    response = requests.get("http://localhost:8000/engines")
    return response.json()

if __name__ == "__main__":
    # Example usage
    print("Available engines:", get_available_engines())
    
    # Extract text from a file
    # result = extract_text_from_file("your_document.pdf")
    # print("Extracted text:", result['text'])
'''
    
    # JavaScript client example
    js_client = '''// JavaScript client example for OCR API

class OCRClient {
    constructor(baseUrl = 'http://localhost:8000') {
        this.baseUrl = baseUrl;
    }
    
    async extractText(file, options = {}) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('language', options.language || 'en');
        formData.append('engines', options.engines || 'auto');
        formData.append('enhance_image', options.enhanceImage || true);
        formData.append('detect_orientation', options.detectOrientation || true);
        
        const response = await fetch(`${this.baseUrl}/extract-text`, {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            throw new Error(`OCR failed: ${response.status}`);
        }
        
        return await response.json();
    }
    
    async getEngines() {
        const response = await fetch(`${this.baseUrl}/engines`);
        return await response.json();
    }
    
    async getConfig() {
        const response = await fetch(`${this.baseUrl}/config`);
        return await response.json();
    }
}

// Example usage
const ocrClient = new OCRClient();

// Handle file upload
document.getElementById('fileInput').addEventListener('change', async (event) => {
    const file = event.target.files[0];
    if (file) {
        try {
            const result = await ocrClient.extractText(file);
            console.log('OCR Result:', result);
            document.getElementById('output').textContent = result.text;
        } catch (error) {
            console.error('OCR Error:', error);
        }
    }
});
'''
    
    # Save examples
    with open('client_example.py', 'w') as f:
        f.write(python_client)
    
    with open('client_example.js', 'w') as f:
        f.write(js_client)
    
    print("✅ Client examples created:")
    print("   • client_example.py")
    print("   • client_example.js")

def main():
    """Main setup and demo function"""
    print("🚀 OCR API SETUP AND DEMO")
    print("=" * 60)
    
    # Check if running in correct directory
    if not Path("ocr_api.py").exists():
        print("❌ Error: ocr_api.py not found in current directory")
        print("Please run this script from the directory containing ocr_api.py")
        return
    
    steps = [
        ("Install System Dependencies", install_system_dependencies),
        ("Install Python Dependencies", install_python_dependencies),
        ("Test Individual Engines", test_individual_engines),
        ("Start API Server", start_api_server),
        ("Demo API Endpoints", demo_api_endpoints),
        ("Demo OCR Extraction", demo_ocr_extraction),
        ("Create Client Examples", create_client_examples),
    ]
    
    for step_name, step_func in steps:
        print(f"\n{'🔄' * 20}")
        print(f"STEP: {step_name}")
        print(f"{'🔄' * 20}")
        
        try:
            success = step_func()
            if success is False:
                print(f"⚠️  Warning: {step_name} had issues but continuing...")
        except KeyboardInterrupt:
            print(f"\n❌ Setup interrupted by user")
            break
        except Exception as e:
            print(f"❌ Error in {step_name}: {e}")
            choice = input(f"Continue anyway? (y/N): ").strip().lower()
            if choice != 'y':
                break
    
    print("\n" + "🎉" * 20)
    print("SETUP AND DEMO COMPLETED!")
    print("🎉" * 20)
    print("\n📋 SUMMARY:")
    print("• OCR API is running on http://localhost:8000")
    print("• API documentation: http://localhost:8000/docs")
    print("• Health check: http://localhost:8000/health")
    print("• Available engines: http://localhost:8000/engines")
    print("\n📁 FILES CREATED:")
    print("• ocr_test_results/ - Engine test results")
    print("• client_example.py - Python client example")
    print("• client_example.js - JavaScript client example")
    print("\n🔗 DEMO LINKS:")
    print("• Test invoice: https://github.com/excid3/receipts/raw/main/examples/invoice.pdf")
    print("• Test receipt: https://ocr.space/Content/Images/receipt-ocr-original.jpg")
    print("• Test handwriting: https://ocr.space/Content/Images/handwriting-sample.jpg")

if __name__ == "__main__":
    main() 