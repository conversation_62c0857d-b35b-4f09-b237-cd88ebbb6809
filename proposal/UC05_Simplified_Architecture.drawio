<mxfile host="app.diagrams.net" modified="2025-06-19T00:00:00.000Z" agent="5.0" etag="simplified-uc05" version="24.5.4" type="device">
  <diagram name="UC05-Simplified-Flow" id="uc05-simplified-architecture">
    <mxGraphModel dx="1200" dy="800" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1400" pageHeight="900" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Title -->
        <mxCell id="title" value="UC05: Agentic AI Liability Decision System - Simplified Flow" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;fontColor=#1f4e79;" vertex="1" parent="1">
          <mxGeometry x="350" y="20" width="700" height="40" as="geometry" />
        </mxCell>
        
        <!-- Input Documents -->
        <mxCell id="input-docs" value="📄 INPUT DOCUMENTS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="100" width="200" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="doc-types" value="• FNOL Reports&#xa;• Medical Records&#xa;• Incident Reports&#xa;• Policy Documents&#xa;• Email Communications" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;align=left;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="50" y="180" width="200" height="100" as="geometry" />
        </mxCell>
        
        <!-- AI Processing -->
        <mxCell id="ai-processing" value="🤖 AI PROCESSING PIPELINE" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="350" y="100" width="250" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="processing-steps" value="1. OCR &amp; Classification (Tesseract + MiniCPM-o)&#xa;2. Evidence Extraction (GPT-4)&#xa;3. Data Sufficiency Check&#xa;4. Legal Precedent Matching (CanLII)&#xa;5. Liability Assessment&#xa;6. Loss Quantum Calculation" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;align=left;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="350" y="180" width="250" height="120" as="geometry" />
        </mxCell>
        
        <!-- Decision Engine -->
        <mxCell id="decision-engine" value="⚖️ DECISION ENGINE" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="700" y="100" width="200" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="decision-components" value="• Canadian Legal Analysis&#xa;• Fault % Calculation&#xa;• Settlement Recommendation&#xa;• Risk Assessment&#xa;• Compliance Check" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;align=left;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="700" y="180" width="200" height="100" as="geometry" />
        </mxCell>
        
        <!-- Output -->
        <mxCell id="output" value="📊 INTELLIGENT OUTPUT" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1000" y="100" width="200" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="output-types" value="• Liability Decision Report&#xa;• Professional Communications&#xa;• Settlement Recommendations&#xa;• Audit Trail&#xa;• Compliance Documentation" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;align=left;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="1000" y="180" width="200" height="100" as="geometry" />
        </mxCell>
        
        <!-- Flow Arrows -->
        <mxCell id="arrow1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#2E7D32;endArrow=block;endSize=8;" edge="1" parent="1" source="input-docs" target="ai-processing">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#2E7D32;endArrow=block;endSize=8;" edge="1" parent="1" source="ai-processing" target="decision-engine">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#2E7D32;endArrow=block;endSize=8;" edge="1" parent="1" source="decision-engine" target="output">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Real Case Examples -->
        <mxCell id="case-examples" value="🎯 REAL CASE VALIDATION" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="350" width="1150" height="40" as="geometry" />
        </mxCell>
        
        <!-- Case 31 -->
        <mxCell id="case31" value="CASE 31: No Frills Slip-and-Fall" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="420" width="350" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="case31-details" value="📋 Input: Store incident report, CCTV footage, customer statement&#xa;🤖 AI Analysis: Water from frozen door, no warning signs, delayed response&#xa;⚖️ Decision: 80% store liability (Occupier's Liability Act)&#xa;📊 Output: Settlement recommendation + professional communication" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;align=left;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="50" y="460" width="350" height="80" as="geometry" />
        </mxCell>
        
        <!-- Case 154 -->
        <mxCell id="case154" value="CASE 154: Elderly Customer Fall" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="425" y="420" width="350" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="case154-details" value="📋 Input: Basic FNOL, limited medical info, family involvement&#xa;🤖 AI Analysis: Insufficient medical documentation detected&#xa;⚖️ Decision: Workflow paused, sensitive communication required&#xa;📊 Output: Professional email for family + privacy-compliant handling" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;align=left;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="425" y="460" width="350" height="80" as="geometry" />
        </mxCell>
        
        <!-- Case 19 -->
        <mxCell id="case19" value="CASE 19: Complex Medical Claim" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="800" y="420" width="350" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="case19-details" value="📋 Input: 15+ medical docs, multiple providers, complex expenses&#xa;🤖 AI Analysis: Parsed all documents, calculated total damages&#xa;⚖️ Decision: $17,886.14 total claim validation&#xa;📊 Output: Detailed breakdown + settlement recommendation" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;align=left;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="800" y="460" width="350" height="80" as="geometry" />
        </mxCell>
        
        <!-- Technology Stack -->
        <mxCell id="tech-stack" value="🛠️ CORE TECHNOLOGY STACK" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="580" width="1150" height="40" as="geometry" />
        </mxCell>
        
        <!-- Backend -->
        <mxCell id="backend-tech" value="BACKEND" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="640" width="180" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="backend-details" value="• FastAPI (Python 3.11+)&#xa;• PostgreSQL + JSONB&#xa;• Redis Caching&#xa;• Docker + Kubernetes" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;align=left;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="50" y="680" width="180" height="80" as="geometry" />
        </mxCell>
        
        <!-- AI/ML -->
        <mxCell id="aiml-tech" value="AI/ML PIPELINE" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="260" y="640" width="180" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="aiml-details" value="• OpenAI GPT-4&#xa;• Anthropic Claude&#xa;• Legal-BERT&#xa;• ChromaDB Vector DB" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;align=left;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="260" y="680" width="180" height="80" as="geometry" />
        </mxCell>
        
        <!-- Legal -->
        <mxCell id="legal-tech" value="LEGAL INTEGRATION" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="470" y="640" width="180" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="legal-details" value="• CanLII API&#xa;• Ontario e-Laws&#xa;• Provincial Regulations&#xa;• Legal Precedent DB" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;align=left;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="470" y="680" width="180" height="80" as="geometry" />
        </mxCell>
        
        <!-- Security -->
        <mxCell id="security-tech" value="SECURITY & COMPLIANCE" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="680" y="640" width="180" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="security-details" value="• AES-256 Encryption&#xa;• PIPEDA Compliance&#xa;• JWT Authentication&#xa;• Audit Trails" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;align=left;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="680" y="680" width="180" height="80" as="geometry" />
        </mxCell>
        
        <!-- Frontend -->
        <mxCell id="frontend-tech" value="USER INTERFACE" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="890" y="640" width="180" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="frontend-details" value="• React 18 + Hooks&#xa;• Tailwind CSS&#xa;• Responsive Design&#xa;• Real-time Updates" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;align=left;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="890" y="680" width="180" height="80" as="geometry" />
        </mxCell>
        
        <!-- Monitoring -->
        <mxCell id="monitoring-tech" value="MONITORING" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1100" y="640" width="100" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="monitoring-details" value="• Prometheus&#xa;• Grafana&#xa;• ELK Stack&#xa;• Alerts" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;align=left;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="1100" y="680" width="100" height="80" as="geometry" />
        </mxCell>
        
        <!-- Key Metrics -->
        <mxCell id="metrics-title" value="📈 KEY PERFORMANCE METRICS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="800" width="300" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="metrics-content" value="• 80% faster processing time&#xa;• 95%+ document accuracy&#xa;• 100% legal consistency&#xa;• $3,000+ savings per claim&#xa;• 4-6 months ROI payback" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;align=left;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="50" y="840" width="300" height="80" as="geometry" />
        </mxCell>
        
        <!-- Business Value -->
        <mxCell id="business-title" value="💰 BUSINESS VALUE" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="400" y="800" width="300" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="business-content" value="• Operational efficiency gains&#xa;• Consistent decision quality&#xa;• Regulatory compliance automation&#xa;• Improved customer satisfaction&#xa;• Competitive market advantage" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;align=left;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="400" y="840" width="300" height="80" as="geometry" />
        </mxCell>
        
        <!-- Compliance -->
        <mxCell id="compliance-title" value="🔒 COMPLIANCE & SECURITY" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="750" y="800" width="300" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="compliance-content" value="• PIPEDA privacy compliance&#xa;• Provincial insurance regulations&#xa;• Immutable audit trails&#xa;• Real-time monitoring&#xa;• Automated reporting" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;align=left;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="750" y="840" width="300" height="80" as="geometry" />
        </mxCell>
        
        <!-- Contact Info -->
        <mxCell id="contact-info" value="📞 CONTACT: dinesh.krishna.ai.ml | 🌐 DEMO: uc05-demo.dineshkrishna.ai | 📁 GITHUB: github.com/dineshkrishna/zurich-uc05" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1100" y="800" width="300" height="120" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
