<mxfile host="app.diagrams.net" modified="2025-06-19T00:00:00.000Z" agent="5.0" etag="xxx" version="24.5.4" type="device">
  <diagram name="UC05-Liability-System" id="uc05-system-architecture">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Title -->
        <mxCell id="title" value="UC05: Agentic AI Liability Decision System" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontStyle=1;fontColor=#1f4e79;" vertex="1" parent="1">
          <mxGeometry x="527" y="20" width="600" height="40" as="geometry" />
        </mxCell>
        
        <!-- Input Layer -->
        <mxCell id="input-layer" value="📄 DOCUMENT INGESTION LAYER" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="100" width="200" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="fnol-docs" value="FNOL Reports" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="30" y="200" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="medical-docs" value="Medical Records" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="150" y="200" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="incident-docs" value="Incident Reports" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="30" y="260" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="policy-docs" value="Policy Documents" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="150" y="260" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- Processing Layer -->
        <mxCell id="processing-layer" value="🔧 AI PROCESSING PIPELINE" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="350" y="100" width="200" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="ocr-processor" value="OCR &amp; Classification&#xa;(Tesseract + MiniCPM-o)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="320" y="200" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="evidence-extractor" value="Evidence Extraction&#xa;(GPT-4 + Legal-BERT)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="460" y="200" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="data-quality" value="Data Sufficiency&#xa;Assessment" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="320" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="workflow-engine" value="Workflow Decision&#xa;Engine (n8n)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="460" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- Legal Intelligence Layer -->
        <mxCell id="legal-layer" value="⚖️ LEGAL INTELLIGENCE ENGINE" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="650" y="100" width="200" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="canlii-api" value="CanLII API&#xa;(Canadian Case Law)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="620" y="200" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="precedent-matcher" value="Precedent Matching&#xa;(ChromaDB + Semantic)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="760" y="200" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="legal-reasoning" value="Legal Reasoning&#xa;(Claude + GPT-4)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="620" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="coverage-analysis" value="Coverage Analysis&#xa;(Policy Interpretation)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="760" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- Decision Layer -->
        <mxCell id="decision-layer" value="🎯 DECISION &amp; CALCULATION ENGINE" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="950" y="100" width="200" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="liability-analyzer" value="Liability Assessment&#xa;(Fault % Calculation)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="920" y="200" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="quantum-calculator" value="Loss Quantum&#xa;Calculator" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="1060" y="200" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="settlement-engine" value="Settlement&#xa;Recommendation" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="920" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="risk-assessment" value="Risk Factor&#xa;Analysis" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="1060" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- Communication Layer -->
        <mxCell id="communication-layer" value="📧 COMMUNICATION &amp; OUTPUT LAYER" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1250" y="100" width="200" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="email-generator" value="Professional Email&#xa;Generation" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="1220" y="200" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="report-generator" value="Decision Report&#xa;Generation" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="1360" y="200" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="stakeholder-comm" value="Stakeholder&#xa;Communication" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="1220" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="compliance-report" value="Compliance&#xa;Reporting" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="1360" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- Data Storage Layer -->
        <mxCell id="storage-layer" value="💾 DATA STORAGE &amp; COMPLIANCE" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="350" y="400" width="600" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="postgresql" value="PostgreSQL&#xa;(Primary Database)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="320" y="500" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="chromadb" value="ChromaDB&#xa;(Vector Database)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="460" y="500" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="redis" value="Redis&#xa;(Caching Layer)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="600" y="500" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="audit-system" value="Audit Trail System&#xa;(PIPEDA Compliant)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="740" y="500" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="encryption" value="AES-256 Encryption&#xa;(Data Protection)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="880" y="500" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- External APIs -->
        <mxCell id="external-apis" value="🌐 EXTERNAL INTEGRATIONS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="400" width="200" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="openai-api" value="OpenAI GPT-4" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="30" y="500" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="claude-api" value="Anthropic Claude" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="130" y="500" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="canlii-external" value="CanLII API" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="30" y="560" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="ontario-laws" value="Ontario e-Laws" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="130" y="560" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- Monitoring Layer -->
        <mxCell id="monitoring-layer" value="📊 MONITORING &amp; ANALYTICS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1050" y="400" width="200" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="prometheus" value="Prometheus&#xa;(Metrics)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="1020" y="500" width="80" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="grafana" value="Grafana&#xa;(Dashboards)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="1120" y="500" width="80" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="elk-stack" value="ELK Stack&#xa;(Logging)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="1220" y="500" width="80" height="60" as="geometry" />
        </mxCell>
        
        <!-- User Interface -->
        <mxCell id="ui-layer" value="🖥️ USER INTERFACE" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1350" y="400" width="200" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="react-frontend" value="React Dashboard&#xa;(Claims Processing)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="1320" y="500" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="mobile-app" value="Mobile Interface&#xa;(Field Adjusters)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="1460" y="500" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- Data Flow Arrows -->
        <mxCell id="arrow1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;" edge="1" parent="1" source="input-layer" target="processing-layer">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;" edge="1" parent="1" source="processing-layer" target="legal-layer">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;" edge="1" parent="1" source="legal-layer" target="decision-layer">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;" edge="1" parent="1" source="decision-layer" target="communication-layer">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Performance Metrics Box -->
        <mxCell id="metrics-box" value="📈 KEY PERFORMANCE METRICS&#xa;&#xa;• Processing Time: 80% faster&#xa;• Document Accuracy: 95%+&#xa;• Legal Consistency: 100%&#xa;• Compliance Score: 100%&#xa;• Cost Reduction: 85%&#xa;• ROI: 4-6 months payback" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;fontSize=12;fontStyle=1;align=left;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="50" y="650" width="300" height="150" as="geometry" />
        </mxCell>
        
        <!-- Technology Stack Box -->
        <mxCell id="tech-stack-box" value="🛠️ TECHNOLOGY STACK&#xa;&#xa;• Backend: FastAPI + Python 3.11&#xa;• Database: PostgreSQL + Redis&#xa;• AI/ML: GPT-4, Claude, Legal-BERT&#xa;• Frontend: React 18 + Tailwind CSS&#xa;• Security: AES-256, JWT, PIPEDA&#xa;• Monitoring: Prometheus + Grafana&#xa;• Deployment: Docker + Kubernetes" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;fontSize=12;fontStyle=1;align=left;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="400" y="650" width="300" height="150" as="geometry" />
        </mxCell>
        
        <!-- Compliance Box -->
        <mxCell id="compliance-box" value="🔒 COMPLIANCE &amp; SECURITY&#xa;&#xa;• PIPEDA (Privacy Act) Compliant&#xa;• Provincial Insurance Regulations&#xa;• AES-256 Encryption&#xa;• Immutable Audit Trails&#xa;• Real-time Compliance Monitoring&#xa;• Automated Regulatory Reporting&#xa;• Data Retention Management" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;fontSize=12;fontStyle=1;align=left;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="750" y="650" width="300" height="150" as="geometry" />
        </mxCell>
        
        <!-- Business Value Box -->
        <mxCell id="business-value-box" value="💰 BUSINESS VALUE&#xa;&#xa;• $3,000+ savings per complex claim&#xa;• 60-80% processing time reduction&#xa;• Consistent liability decisions&#xa;• Improved customer satisfaction&#xa;• Regulatory compliance automation&#xa;• Scalable to 10x claim volume&#xa;• Competitive market advantage" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;fontSize=12;fontStyle=1;align=left;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="1100" y="650" width="300" height="150" as="geometry" />
        </mxCell>
        
        <!-- Legend -->
        <mxCell id="legend" value="🎯 SYSTEM LEGEND" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6e6e6;strokeColor=#666666;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1450" y="650" width="150" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-input" value="Input Layer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1450" y="700" width="70" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-processing" value="Processing" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1530" y="700" width="70" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-ai" value="AI/ML" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1450" y="730" width="70" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-legal" value="Legal Engine" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1530" y="730" width="70" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-decision" value="Decision" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1450" y="760" width="70" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-storage" value="Storage" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1530" y="760" width="70" height="20" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
