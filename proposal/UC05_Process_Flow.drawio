<mxfile host="app.diagrams.net" modified="2025-06-19T00:00:00.000Z" agent="5.0" etag="flow-diagram" version="24.5.4" type="device">
  <diagram name="UC05-Process-Flow" id="uc05-process-flow">
    <mxGraphModel dx="1400" dy="900" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="1200" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- Title -->
        <mxCell id="title" value="UC05: Agentic AI Liability Decision Process Flow" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontStyle=1;fontColor=#1f4e79;" vertex="1" parent="1">
          <mxGeometry x="400" y="20" width="800" height="40" as="geometry" />
        </mxCell>

        <!-- START -->
        <mxCell id="start" value="🚀 START&#xa;Claim Received" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="100" width="120" height="80" as="geometry" />
        </mxCell>

        <!-- Document Upload -->
        <mxCell id="doc-upload" value="📄 Document Upload&#xa;• FNOL Reports&#xa;• Medical Records&#xa;• Incident Reports&#xa;• Policy Documents" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="220" y="100" width="150" height="80" as="geometry" />
        </mxCell>

        <!-- OCR Processing -->
        <mxCell id="ocr-process" value="🔍 OCR &amp; Classification&#xa;• Tesseract OCR&#xa;• MiniCPM-o&#xa;• Document Type Detection&#xa;• Quality Assessment" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="420" y="100" width="150" height="80" as="geometry" />
        </mxCell>

        <!-- Evidence Extraction -->
        <mxCell id="evidence-extract" value="🧠 Evidence Extraction&#xa;• GPT-4 Analysis&#xa;• Fact Identification&#xa;• Party Information&#xa;• Incident Details" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="620" y="100" width="150" height="80" as="geometry" />
        </mxCell>

        <!-- Data Sufficiency Check -->
        <mxCell id="data-check" value="📊 Data Sufficiency&#xa;Assessment" style="rhombus;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="820" y="100" width="120" height="80" as="geometry" />
        </mxCell>

        <!-- Insufficient Data Path -->
        <mxCell id="insufficient-data" value="❌ Insufficient Data" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="820" y="220" width="120" height="40" as="geometry" />
        </mxCell>

        <!-- Generate Communication -->
        <mxCell id="gen-comm" value="📧 Generate&#xa;Communication&#xa;• Missing Doc Request&#xa;• Professional Email&#xa;• Stakeholder Sensitive" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="820" y="300" width="120" height="80" as="geometry" />
        </mxCell>

        <!-- Pause Workflow -->
        <mxCell id="pause-workflow" value="⏸️ Pause Workflow&#xa;Await Documents" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="820" y="420" width="120" height="60" as="geometry" />
        </mxCell>

        <!-- Legal Precedent Analysis -->
        <mxCell id="legal-analysis" value="⚖️ Legal Precedent&#xa;Analysis&#xa;• CanLII Search&#xa;• Case Matching&#xa;• Semantic Similarity&#xa;• Provincial Law" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="1020" y="100" width="140" height="80" as="geometry" />
        </mxCell>

        <!-- Liability Assessment -->
        <mxCell id="liability-assess" value="🎯 Liability Assessment&#xa;• Negligence Analysis&#xa;• Duty of Care&#xa;• Causation Review&#xa;• Fault Allocation" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="1200" y="100" width="140" height="80" as="geometry" />
        </mxCell>

        <!-- Fault Percentage Decision -->
        <mxCell id="fault-decision" value="📊 Fault Percentage&#xa;Decision" style="rhombus;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1380" y="100" width="120" height="80" as="geometry" />
        </mxCell>

        <!-- Loss Quantum Calculation -->
        <mxCell id="loss-quantum" value="💰 Loss Quantum&#xa;Calculation&#xa;• Medical Expenses&#xa;• Lost Wages&#xa;• Pain &amp; Suffering&#xa;• Future Damages" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="1200" y="220" width="140" height="80" as="geometry" />
        </mxCell>

        <!-- Settlement Recommendation -->
        <mxCell id="settlement-rec" value="🤝 Settlement&#xa;Recommendation&#xa;• Amount Calculation&#xa;• Risk Assessment&#xa;• Reserve Setting" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="1380" y="220" width="140" height="80" as="geometry" />
        </mxCell>

        <!-- Generate Final Report -->
        <mxCell id="final-report" value="📋 Generate Final Report&#xa;• Decision Summary&#xa;• Legal Reasoning&#xa;• Supporting Evidence&#xa;• Recommendations" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="1200" y="340" width="140" height="80" as="geometry" />
        </mxCell>

        <!-- Compliance Check -->
        <mxCell id="compliance-check" value="🔒 Compliance Check&#xa;• PIPEDA Validation&#xa;• Audit Trail Creation&#xa;• Data Encryption&#xa;• Regulatory Reporting" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="1380" y="340" width="140" height="80" as="geometry" />
        </mxCell>

        <!-- Communication Generation -->
        <mxCell id="comm-generation" value="📧 Communication&#xa;Generation&#xa;• Decision Letter&#xa;• Settlement Offer&#xa;• Status Update&#xa;• Professional Tone" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="1200" y="460" width="140" height="80" as="geometry" />
        </mxCell>

        <!-- Quality Assurance -->
        <mxCell id="quality-check" value="✅ Quality Assurance&#xa;Review" style="rhombus;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1380" y="460" width="120" height="80" as="geometry" />
        </mxCell>

        <!-- Manual Review Required -->
        <mxCell id="manual-review" value="👨‍💼 Manual Review&#xa;Required" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="1380" y="580" width="120" height="60" as="geometry" />
        </mxCell>

        <!-- Final Output -->
        <mxCell id="final-output" value="📤 Final Output&#xa;• Decision Report&#xa;• Communications&#xa;• Audit Trail&#xa;• Next Steps" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="1200" y="580" width="140" height="80" as="geometry" />
        </mxCell>

        <!-- END -->
        <mxCell id="end" value="🏁 END&#xa;Case Processed" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1020" y="580" width="120" height="80" as="geometry" />
        </mxCell>

        <!-- Flow Arrows -->
        <mxCell id="arrow1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#2E7D32;endArrow=block;endSize=6;" edge="1" parent="1" source="start" target="doc-upload">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#2E7D32;endArrow=block;endSize=6;" edge="1" parent="1" source="doc-upload" target="ocr-process">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#2E7D32;endArrow=block;endSize=6;" edge="1" parent="1" source="ocr-process" target="evidence-extract">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#2E7D32;endArrow=block;endSize=6;" edge="1" parent="1" source="evidence-extract" target="data-check">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- Insufficient Data Flow -->
        <mxCell id="arrow-insufficient" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#D32F2F;endArrow=block;endSize=6;" edge="1" parent="1" source="data-check" target="insufficient-data">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="label-insufficient" value="No" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#D32F2F;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="880" y="190" width="30" height="20" as="geometry" />
        </mxCell>

        <mxCell id="arrow5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#D32F2F;endArrow=block;endSize=6;" edge="1" parent="1" source="insufficient-data" target="gen-comm">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#D32F2F;endArrow=block;endSize=6;" edge="1" parent="1" source="gen-comm" target="pause-workflow">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- Sufficient Data Flow -->
        <mxCell id="arrow-sufficient" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#2E7D32;endArrow=block;endSize=6;" edge="1" parent="1" source="data-check" target="legal-analysis">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="label-sufficient" value="Yes" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#2E7D32;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="950" y="120" width="30" height="20" as="geometry" />
        </mxCell>

        <mxCell id="arrow7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#2E7D32;endArrow=block;endSize=6;" edge="1" parent="1" source="legal-analysis" target="liability-assess">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#2E7D32;endArrow=block;endSize=6;" edge="1" parent="1" source="liability-assess" target="fault-decision">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#2E7D32;endArrow=block;endSize=6;" edge="1" parent="1" source="fault-decision" target="loss-quantum">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#2E7D32;endArrow=block;endSize=6;" edge="1" parent="1" source="fault-decision" target="settlement-rec">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#2E7D32;endArrow=block;endSize=6;" edge="1" parent="1" source="loss-quantum" target="final-report">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#2E7D32;endArrow=block;endSize=6;" edge="1" parent="1" source="settlement-rec" target="compliance-check">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#2E7D32;endArrow=block;endSize=6;" edge="1" parent="1" source="final-report" target="comm-generation">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#2E7D32;endArrow=block;endSize=6;" edge="1" parent="1" source="compliance-check" target="quality-check">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#2E7D32;endArrow=block;endSize=6;" edge="1" parent="1" source="comm-generation" target="quality-check">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- Quality Check Branches -->
        <mxCell id="arrow-quality-pass" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#2E7D32;endArrow=block;endSize=6;" edge="1" parent="1" source="quality-check" target="final-output">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="label-quality-pass" value="Pass" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#2E7D32;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1350" y="540" width="40" height="20" as="geometry" />
        </mxCell>

        <mxCell id="arrow-quality-fail" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#D32F2F;endArrow=block;endSize=6;" edge="1" parent="1" source="quality-check" target="manual-review">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="label-quality-fail" value="Review&#xa;Required" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#D32F2F;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1440" y="540" width="50" height="30" as="geometry" />
        </mxCell>

        <mxCell id="arrow16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#2E7D32;endArrow=block;endSize=6;" edge="1" parent="1" source="final-output" target="end">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- Feedback Loop from Pause -->
        <mxCell id="feedback-arrow" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#FF9800;endArrow=block;endSize=6;dashed=1;" edge="1" parent="1" source="pause-workflow" target="doc-upload">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="880" y="520" />
              <mxPoint x="50" y="520" />
              <mxPoint x="50" y="60" />
              <mxPoint x="295" y="60" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="feedback-label" value="Documents&#xa;Received" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#FF9800;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="400" y="40" width="80" height="30" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>